import { NextRequest, NextResponse } from "next/server";
import { sendToUser } from "@/lib/socket";

export async function POST(request: NextRequest) {
  const data = await request.json();
  const url = new URL(request.url);
  const userId = url.searchParams.get("userId");

  // Extract images from the callback data
  if (data?.prompt?.images && Array.isArray(data.prompt.images) && userId) {
    // Send to specific user only
    sendToUser(userId, {
      type: "new-images",
      images: data.prompt.images,
      promptId: data.prompt.id,
      tuneId: data.prompt.tune_id
    });
  }

  return NextResponse.json({
    success: true,
  });
}
