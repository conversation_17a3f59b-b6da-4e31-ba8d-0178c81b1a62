"use client";

import React, { ReactNode } from "react";
import { motion } from "framer-motion";

interface ScreenContentContainerProps {
  children: ReactNode;
}

const ScreenContentContainer: React.FC<ScreenContentContainerProps> = ({ children }) => {
  return (
    <motion.div
      className="bg-black/20 backdrop-blur-sm rounded-2xl border border-white/10 shadow-xl overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1 }}
    >
      {children}
    </motion.div>
  );
};

export default ScreenContentContainer;
