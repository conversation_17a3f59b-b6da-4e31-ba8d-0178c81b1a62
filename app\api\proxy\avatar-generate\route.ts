import { NextRequest, NextResponse } from "next/server";
import { createAstriaAvatarFormData } from "@/lib/astria-form-utils";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const prompt = formData.get("prompt") as string;
    const user = formData.get("user") as string;
    const inputImage = formData.get("input_image") as File | null;
    const controlnet = formData.get("controlnet") as string | null;
    const denoisingStrength = formData.get("denoising_strength") as string | null;
    const style = formData.get("style") as string | null;

    // Hardcoded model ID for avatar generation
    const modelId = "666678";

    if (!prompt) {
      return NextResponse.json(
        { error: "Prompt is required" },
        { status: 400 }
      );
    }

    if (!user) {
      return NextResponse.json(
        { error: "User ID is required" },
        { status: 400 }
      );
    }

    // Create Astria FormData using centralized utility
    const astriaFormData = createAstriaAvatarFormData({
      prompt,
      user,
      modelId,
      inputImage,
      controlnet: controlnet || "depth", 
      denoisingStrength,
      style,
      backendVersion: "0",
    });

    console.log("astria formdata", astriaFormData);

    // Call Astria API
    const url = `${process.env.NEXT_PUBLIC_ASTRIA_BASE_URL}/tunes/${modelId}/prompts`;
    const res = await fetch(url, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${process.env.NEXT_PUBLIC_ASTRIA_API_KEY}`,
      },
      body: astriaFormData,
    });

    if (!res.ok) {
      const errorData = await res.json();
      console.error("Astria API Error:", errorData);
      return NextResponse.json(
        { error: res.statusText },
        { status: res.status }
      );
    }

    const data = await res.json();

    // Poll for the generated image
    const promptId = data.id;
    const pollUrl = `${process.env.NEXT_PUBLIC_ASTRIA_BASE_URL}/prompts/${promptId}`;

    let imageUrl = null;
    let attempts = 0;
    const maxAttempts = 30; // Maximum number of polling attempts

    while (!imageUrl && attempts < maxAttempts) {
      attempts++;

      // Wait for 2 seconds between polling attempts
      await new Promise(resolve => setTimeout(resolve, 2000));

      const pollRes = await fetch(pollUrl, {
        headers: {
          Authorization: `Bearer ${process.env.NEXT_PUBLIC_ASTRIA_API_KEY}`,
        },
      });

      if (!pollRes.ok) {
        continue;
      }

      const pollData = await pollRes.json();

      if (pollData.images && pollData.images.length > 0) {
        imageUrl = pollData.images[0];
        break;
      }
    }

    if (!imageUrl) {
      return NextResponse.json(
        { error: "Timeout waiting for image generation" },
        { status: 408 }
      );
    }

    return NextResponse.json({ imageUrl });
  } catch (error) {
    console.error("Error generating avatar:", error);
    return NextResponse.json(
      { error: "Failed to generate avatar" },
      { status: 500 }
    );
  }
}
