"use client";
import { Images } from "lucide-react";
import BurstModeLoadingScreen from "../ui/GenerationLoadingScreen";
import DownloadButton from "./DownloadButton";

interface DisplayImageProps {
  loading: boolean;
  images: string[];
}

const DisplayImage = ({ loading, images }: DisplayImageProps) => {
  return (
    <div className="relative h-full">
      {images.length > 0 ? (
        <div className="space-y-4">
          {/* Download Button */}
          <div className="flex justify-end">
            <DownloadButton images={images} />
          </div>

          {/* Images Grid */}
          <div
            className={`grid gap-4 grid-cols-${images.length > 4 ? 3 : images.length}`}
          >
            {images?.map((image, index) => (
              <div
                key={index}
                className="relative aspect-square rounded overflow-hidden"
              >
                <img
                  src={image}
                  alt={`Generated image ${index + 1}`}
                  className="w-full h-full object-contain"
                />
              </div>
            ))}
          </div>
        </div>
      ) : loading ? (
        <LoadingState />
      ) : (
        <EmptyState />
      )}
    </div>
  );
};

DisplayImage.displayName = "DisplayImage";

export default DisplayImage;

function LoadingState() {
  return (
    <div className="flex items-center justify-center h-full">
      <BurstModeLoadingScreen
        message="Generating Images..."
        className="h-fit py-20 rounded-lg"
      />
    </div>
  );
}

function EmptyState() {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="flex items-center justify-center h-full w-full">
        <div className="text-center text-gray-400">
          <div className="mb-4">
            <Images className="h-10 w-10 m-auto" />
          </div>
          <p>Generated images will appear here</p>
          <p className="text-sm text-gray-500 mt-2">
            Start by describing what you want to create.
          </p>
        </div>
      </div>
    </div>
  );
}
