// Store SSE connections by user ID
const userConnections = new Map<string, Set<ReadableStreamDefaultController>>();

export const addUserConnection = (userId: string, controller: ReadableStreamDefaultController) => {
  if (!userConnections.has(userId)) {
    userConnections.set(userId, new Set());
  }
  userConnections.get(userId)!.add(controller);
};

export const removeUserConnection = (userId: string, controller: ReadableStreamDefaultController) => {
  const userControllers = userConnections.get(userId);
  if (userControllers) {
    userControllers.delete(controller);
    if (userControllers.size === 0) {
      userConnections.delete(userId);
    }
  }
};

export const sendToUser = (userId: string, data: any) => {
  const userControllers = userConnections.get(userId);
  if (!userControllers) return;

  const message = `data: ${JSON.stringify(data)}\n\n`;
  const disconnectedControllers: ReadableStreamDefaultController[] = [];

  userControllers.forEach(controller => {
    try {
      controller.enqueue(new TextEncoder().encode(message));
    } catch (error) {
      // Mark for removal
      disconnectedControllers.push(controller);
    }
  });

  // Clean up disconnected controllers
  disconnectedControllers.forEach(controller => {
    removeUserConnection(userId, controller);
  });
};
