"use client"

import { But<PERSON> } from "@heroui/button";
import { useRouter } from "next/navigation";

const TextContent = ({
  title,
  description,
  testimonial,
  image,
  route,
}: {
  title: string;
  description: string;
  testimonial: {
    quote: string;
    author: string;
    role: string;
    avatar: string;
  };
  image: string;
  route: string;
}) => {
  const router = useRouter();

  return (
    <div className="flex flex-col gap-6 items-start">
      <h2 className="font-bold text-3xl lg:text-4xl bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
        {title}
      </h2>
      <p className="text-lg text-muted-foreground leading-relaxed">
        {description}
      </p>
      <div className="rounded-xl p-6 bg-secondary/50 backdrop-blur-sm border shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
        <p className="italic text-muted-foreground">"{testimonial.quote}"</p>
        <div className="flex items-center gap-4 mt-4">
          <img
            src={testimonial.avatar}
            alt=""
            className="rounded-full border-2 border-primary/20 w-10 h-10"
          />
          <p className="font-medium">
            {testimonial.author}, {testimonial.role}
          </p>
        </div>
      </div>
      <Button
        className="mt-4 bg-primary/10 hover:bg-primary/20 text-primary border border-primary/30 rounded-xl px-8 py-6 text-lg font-medium transition-all duration-300 hover:scale-105"
        onClick={() => router.push(route)}
      >
        Try it
      </Button>
    </div>
  );
};

export default TextContent;
