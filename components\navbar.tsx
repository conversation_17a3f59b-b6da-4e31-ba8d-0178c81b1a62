"use client";

import React from "react";
import { But<PERSON> } from "@heroui/button";
import Link from "next/link";
import { CreateButton } from "./ui/CreateButton";
import { cn } from "@/lib/utils";
import { Poppins } from "next/font/google";
import { useSidebar } from "@/components/ui/Sidebar";
import Image from "next/image";
import UserDropdown from "./UserDropdown";
import { AlignJustify } from "lucide-react";
const font = Poppins({ weight: "600", subsets: ["latin"] });

const Navbar = () => {
  const { setOpen } = useSidebar();

  return (
    <div className="fixed w-full z-50 flex justify-between items-center py-2 px-4 xl:px-8 border-b border-primary/10 bg-gray-800/50 backdrop-blur-sm h-16">
      <div className="flex items-center">
        <div className="flex md:hidden items-center pr-3">
          <AlignJustify
            onClick={() => setOpen((prev) => !prev)}
            className="text-slate-900 dark:text-slate-200 h-6 w-6 cursor-pointer"
          />
        </div>
        <Link href="/">
          <div className="hidden md:flex items-center gap-x-2">
            <Image
              src="/app/icon-transparent.png"
              alt="Burst Mode Icon"
              width={70}
              height={70}
              className="object-contain"
            />
            <h1
              className={cn(
                "text-xl md:text-4xl font-bold bg-gradient-to-r from-purple-400 via-blue-500 to-purple-400 bg-clip-text text-transparent animate-[gradient-slide_5s_linear_infinite] bg-[length:200%_200%]",
                font.className
              )}
            >
              Burst Mode
            </h1>
          </div>
        </Link>
      </div>
      <div className="hidden md:flex items-center gap-x-4">
        <Link href="/model">
          <CreateButton />
        </Link>
        <Link href="/pricing">
          <Button className="relative inline-flex h-10 overflow-hidden rounded-lg p-[2px]">
            <span className="absolute inset-[-1000%] animate-[spin_2s_linear_infinite] bg-[conic-gradient(from_90deg_at_50%_50%,#E2CBFF_0%,#393BB2_50%,#E2CBFF_100%)]" />
            <span className="inline-flex h-9 w-full cursor-pointer items-center justify-center rounded-lg bg-slate-950 px-3 py-0 text-sm font-medium text-white backdrop-blur-3xl">
              Pricing
            </span>
          </Button>
        </Link>
        <Link href="/gallery">
          <Button
            variant="solid"
            size="sm"
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            Gallery
          </Button>
        </Link>
        <UserDropdown />
      </div>
      <div className="flex md:hidden items-center gap-x-4">
        <Link href="/model">
          <CreateButton />
        </Link>
        <UserDropdown />
      </div>
    </div>
  );
};

export default Navbar;
