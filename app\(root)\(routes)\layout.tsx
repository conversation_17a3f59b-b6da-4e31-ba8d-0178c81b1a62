import Navbar from "@/components/Navbar";
import { SidebarProvider } from "@/components/ui/sidebar/Sidebar";
import MobileSidebarWrapper from "@/components/ui/sidebar/MobileSidebarWrapper";
import { UserProvider } from "@/context/UserContext";

const RootLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <UserProvider>
      <SidebarProvider>
        <div className="">
          <Navbar />
          <MobileSidebarWrapper />
          <main className="h-full lg:pt-0 pt-12">{children}</main>
        </div>
      </SidebarProvider>
    </UserProvider>
  );
};

export default RootLayout;
