import { auth, db } from "../config";
import {
  GoogleAuthProvider,
  createUserWithEmailAndPassword,
  signInWithPopup,
  signInWithRedirect,
  EmailAuthProvider,
  signOut,
  getRedirectResult,
  signInWithEmailAndPassword,
  sendEmailVerification,
  setPersistence,
  browserSessionPersistence,
  browserLocalPersistence,
  User,
  reauthenticateWithCredential,
  updateProfile,
  updateEmail,
  updatePassword,
  deleteUser
} from "firebase/auth";
import { doc, setDoc, getDoc, deleteDoc } from "firebase/firestore";

/**
 * Signup using Firebase
 * @param email - The user's email (string)
 * @param fullname - The user's fullname (string)
 * @param password - The password entered by the user (string)
 * @returns { success: boolean, message?: string }
 */
export const register = async (
  email: string,
  fullname: string,
  password: string
): Promise<{ success: boolean; message: string }> => {
  try {
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      email,
      password
    );

    const user = userCredential.user;

    await setDoc(doc(db, "users", user.uid), { fullname, email });

    try {
      await sendEmailVerification(user);
    } catch (error: any) {
      return {
        success: false,
        message: error.message
          ? error.message
          : "Failed to send verification email",
      };
    }

    return { success: true, message: "Registration successful" };
  } catch (error: any) {
    let errorMessage = "Registration failed";

    if (error.code) {
      switch (error.code) {
        case "auth/email-already-in-use":
          errorMessage =
            "The email address is already in use by another account.";
          break;
        case "auth/invalid-email":
          errorMessage = "The email address is not valid.";
          break;
        case "auth/operation-not-allowed":
          errorMessage =
            "Email/password accounts are not enabled. Please contact support.";
          break;
        case "auth/weak-password":
          errorMessage =
            "The password is too weak. It should be at least 6 characters long.";
          break;
        default:
          errorMessage = error.message ? error.message : errorMessage;
          break;
      }
    }

    return {
      success: false,
      message: errorMessage,
    };
  }
};

/**
 * Login using Firebase Authentication
 * @param email - The user's email (string)
 * @param password - The password entered by the user (string)
 * @param rememberMe - Boolean indicating if the session should persist across browser restarts
 * @returns { success: boolean; verified: boolean; user?: { email: string | null; fullname: string | null }; message: string }
 */
export const login = async (
  email: string,
  password: string,
  rememberMe: boolean
): Promise<{
  success: boolean;
  verified: boolean;
  user?: User;
  message: string;
}> => {
  try {
    // Set persistence based on the "Remember Me" option
    const persistence = rememberMe
      ? browserLocalPersistence
      : browserSessionPersistence;
    await setPersistence(auth, persistence);

    // Sign in the user
    const userCredential = await signInWithEmailAndPassword(
      auth,
      email,
      password
    );
    const user = userCredential.user;
    const token = await user.getIdTokenResult();
    console.log(token.claims);

    if (!user.emailVerified) {
      return {
        success: true,
        verified: false,
        user: user,
        message:
          "Email not verified. Please verify your email before logging in.",
      };
    }

    const userDoc = await getDoc(doc(db, "users", user.uid));
    const fullname = userDoc.exists() ? userDoc.data().fullname : null;

    return {
      success: true,
      verified: true,
      user: user,
      message: "Login successful.",
    };
  } catch (error: any) {
    let errorMessage = "Login failed";

    if (error.code) {
      switch (error.code) {
        case "auth/invalid-email":
          errorMessage = "The email address is not valid.";
          break;
        case "auth/user-disabled":
          errorMessage = "This user account has been disabled.";
          break;
        case "auth/user-not-found":
          errorMessage = "No user found with this email address.";
          break;
        case "auth/wrong-password":
          errorMessage = "Incorrect password.";
          break;
        default:
          errorMessage = error.message ? error.message : errorMessage;
          break;
      }
    }

    return {
      success: false,
      verified: false,
      user: undefined,
      message: errorMessage,
    };
  }
};

/**
 * Sign in with Google
 * @returns { success: boolean; user?: { email: string | null; fullname: string | null; uid: string }; message: string }
 */
export const signInWithGoogle = async (): Promise<{
  success: boolean;
  user?: { email: string | null; fullname: string | null; uid: string };
  message: string;
}> => {
  try {
    const provider = new GoogleAuthProvider();

    // You can use `signInWithPopup` or `signInWithRedirect`
    const result = await signInWithPopup(auth, provider);
    const user = result.user;

    // Check if user already exists in Firestore
    const userDocRef = doc(db, "users", user.uid);
    const userDoc = await getDoc(userDocRef);

    if (!userDoc.exists()) {
      // If user is new, save to Firestore
      await setDoc(userDocRef, {
        fullname: user.displayName || "Google User",
        email: user.email,
      });
    }

    return {
      success: true,
      user: {
        uid: user.uid,
        email: user.email,
        fullname: user.displayName || "Google User",
      },
      message: "Google sign-in successful",
    };
  } catch (error: any) {
    console.error("Google Sign-In Error:", error);
    return {
      success: false,
      message: error.message || "Google sign-in failed",
    };
  }
};

/**
 * Handle Redirect Sign-In with Google
 * (Use this if you prefer `signInWithRedirect` instead of `signInWithPopup`)
 */
export const handleGoogleRedirectSignIn = async (): Promise<{
  success: boolean;
  user?: { email: string | null; fullname: string | null; uid: string };
  message: string;
}> => {
  try {
    const result = await getRedirectResult(auth);
    if (!result) {
      return { success: false, message: "No redirect result found" };
    }

    const user = result.user;

    // Check if user already exists in Firestore
    const userDocRef = doc(db, "users", user.uid);
    const userDoc = await getDoc(userDocRef);

    if (!userDoc.exists()) {
      // If user is new, save to Firestore
      await setDoc(userDocRef, {
        fullname: user.displayName || "Google User",
        email: user.email,
      });
    }

    return {
      success: true,
      user: {
        uid: user.uid,
        email: user.email,
        fullname: user.displayName || "Google User",
      },
      message: "Google redirect sign-in successful",
    };
  } catch (error: any) {
    console.error("Google Redirect Sign-In Error:", error);
    return {
      success: false,
      message: error.message || "Google redirect sign-in failed",
    };
  }
};

/**
 * Logout from Firebase
 * @returns { success: boolean; message: string }
 */
export const logout = async (): Promise<{ success: boolean; message: string }> => {
  try {
    await signOut(auth);
    return { success: true, message: "Logout successful" };
  } catch (error: any) {
    return { success: false, message: error.message || "Logout failed" };
  }
};

/**
 * Update user profile (Name & Email)
 */
export const updateUserProfile = async ({ fullname, email }: { fullname: string; email: string }) => {
  try {
    if (!auth.currentUser) throw new Error("User not logged in");

    await updateProfile(auth.currentUser, { displayName: fullname });
    await updateEmail(auth.currentUser, email);

    await setDoc(doc(db, "users", auth.currentUser.uid), { fullname, email }, { merge: true });

    return { success: true };
  } catch (error: any) {
    return { success: false, message: error.message };
  }
};

/**
 * Update user password
 */
export const updateUserPassword = async (currentPassword: string, newPassword: string) => {
  try {
    if (!auth.currentUser) throw new Error("User not logged in");
    
    // Create a credential using the EmailAuthProvider
    const credential = EmailAuthProvider.credential(
      auth.currentUser.email || "", 
      currentPassword
    );
    await reauthenticateWithCredential(auth.currentUser, credential);

    await updatePassword(auth.currentUser, newPassword);

    return { success: true };
  } catch (error: any) {
    return { success: false, message: error.message };
  }
};

/**
 * Delete user account
 */
export const deleteUserAccount = async () => {
  try {
    if (!auth.currentUser) throw new Error("User not logged in");

    await deleteDoc(doc(db, "users", auth.currentUser.uid));
    await deleteUser(auth.currentUser);

    return { success: true };
  } catch (error: any) {
    return { success: false, message: error.message };
  }
};
