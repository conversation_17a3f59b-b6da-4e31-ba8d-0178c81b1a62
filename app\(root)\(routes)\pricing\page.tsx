import React from "react";
import Pricing from "@/components/pricing/Pricing";
import { Metadata } from 'next';
import <PERSON>ript from 'next/script';
import { PLANS } from "@/lib/constants";

export const metadata: Metadata = {
  title: "Pricing Plans & Subscription Options | Burst Mode",
  description: "Choose the perfect Burst Mode AI photography plan for your needs. Flexible pricing options from Starter to Enterprise with monthly and yearly subscription options.",
  keywords: [
    "AI photography pricing",
    "Burst Mode subscription",
    "AI image enhancement plans",
    "photo enhancement pricing",
    "AI photography subscription",
    "Burst Mode pricing plans",
    "professional headshot pricing",
    "food photography pricing",
    "product photography pricing",
    "image upscaling cost"
  ],
  openGraph: {
    title: "Pricing Plans & Subscription Options | Burst Mode",
    description: "Choose the perfect Burst Mode AI photography plan for your needs. Flexible pricing options from Starter to Enterprise with monthly and yearly subscription options.",
    url: "https://burstmode.ai/pricing",
    siteName: "Burst Mode AI",
    images: [
      {
        url: "https://burstmode.ai/app/og-icon.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Pricing Plans",
      }
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Pricing Plans & Subscription Options | Burst Mode",
    description: "Choose the perfect Burst Mode AI photography plan for your needs. Flexible pricing options for all requirements.",
    images: [
      {
        url: "https://burstmode.ai/app/og-icon.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Pricing Plans",
      }
    ],
  },
  alternates: {
    canonical: "https://burstmode.ai/pricing",
  },
};

const PricingPage = () => {
  return (
    <>
      {/* Structured Data for SEO */}
      <Script
        id="pricing-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "Pricing Plans & Subscription Options",
            "description": "Choose the perfect Burst Mode AI photography plan for your needs. Flexible pricing options from Starter to Enterprise with monthly and yearly subscription options.",
            "url": "https://burstmode.ai/pricing",
            "mainEntity": {
              "@type": "ItemList",
              "itemListElement": PLANS.map((plan, index) => ({
                "@type": "ListItem",
                "position": index + 1,
                "item": {
                  "@type": "Product",
                  "name": `${plan.name} Plan`,
                  "description": plan.description,
                  "offers": {
                    "@type": "Offer",
                    "price": plan.price,
                    "priceCurrency": "USD",
                    "priceValidUntil": new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],
                    "availability": "https://schema.org/InStock",
                    "url": "https://burstmode.ai/pricing",
                    "seller": {
                      "@type": "Organization",
                      "name": "Burst Mode AI",
                      "url": "https://burstmode.ai"
                    }
                  }
                }
              }))
            }
          })
        }}
      />
      <Pricing />
    </>
  );
};

export default PricingPage;