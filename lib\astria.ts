// lib/astria.ts

import axios from 'axios';

// const API_KEY = process.env.ASTRIA_API_KEY;
// console.log("API Key: ", API_KEY);

type AstriaResponse<T> = {
  success: boolean;
  message: string;
  data?: T;
};

export async function finetuneModelOnAstria(params: {
  user: string;
  title: string;
  images: File[];
}): Promise<AstriaResponse<{ tuneId: string }>> {
  const { user, title, images } = params;

  // Basic validation
  if (!user) {
    return { success: false, message: 'user is required' };
  }
  if (!title) {
    return { success: false, message: 'title is required' };
  }
  if (!images || images.length === 0) {
    return { success: false, message: 'At least one image (File) is required' };
  }

  const API_KEY = process.env.NEXT_PUBLIC_ASTRIA_API_KEY;
  console.log("API Key: ", API_KEY);
  const API_URL = 'https://api.astria.ai/tunes';

  try {
    // 1. Prepare FormData
    const files: FormData = new FormData();

    images.forEach((image) => {
      files.append('tune[images][]', image, image.name);
    });
    
    console.log("Images appended successfully")

    // Fine-tuning session data
    const fineTuneData = {
      "tune[title]": title,
      "tune[name]": user,
      "tune[branch]": "flux1",
      "tune[model_type]": "lora",
      "tune[preset]": "flux-lora-portrait"
    };

    // Add fine-tune data to the FormData object
    Object.entries(fineTuneData).forEach(([key, value]) => {
      files.append(key, value);
    });

    const fineTuneResponse = await axios.post(API_URL, files, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'multipart/form-data'
      }
    });

    if (fineTuneResponse.status !== 201) {
      return {
        success: false,
        message: `Astria tune creation failed: ${JSON.stringify(
          fineTuneResponse.data
        )}`,
      };
    }

    const fineTuneDetails = fineTuneResponse.data;
    const {
      id: tuneId,
      url: fineTuneUrl,
      eta,
      started_training_at,
    } = fineTuneDetails;

    // Optional logging
    if (started_training_at) {
      const etaDate = new Date(eta);
      const startedDate = new Date(started_training_at);
      const etaInMinutes = (etaDate.getTime() - startedDate.getTime()) / 60000;
      console.log(`ETA in minutes: ${etaInMinutes}`);
    } else {
      console.log('Training has not started yet.');
    }

    // 3. Poll for training completion
    while (true) {
      const statusResponse = await axios.get(fineTuneUrl, {
        headers: { Authorization: `Bearer ${API_KEY}` },
      });
      if (statusResponse.data.expires_at) {
        break; // Fine-tuning is complete
      }
      // Wait 60 seconds
      await new Promise((resolve) => setTimeout(resolve, 60_000));
    }

    return {
      success: true,
      message: 'Model fine-tuned successfully',
      data: { tuneId },
    };
  } catch (error: any) {
    return {
      success: false,
      message: error?.message || 'Unknown error occurred',
    };
  }
}

export async function generateImagesOnAstria(params: {
  user: string;
  title: string;
  tuneId: string;
  prompt: string;
}): Promise<AstriaResponse<string[]>> {
  const { user, title, tuneId, prompt } = params;

  // Basic validation
  if (!user) {
    return { success: false, message: 'user is required' };
  }
  if (!title) {
    return { success: false, message: 'title is required' };
  }
  if (!tuneId) {
    return { success: false, message: 'tuneId is required' };
  }
  if (!prompt) {
    return { success: false, message: 'prompt is required' };
  }

  const API_KEY = process.env.ASTRIA_API_KEY || 'YOUR_ASTRIA_API_KEY';

  try {
    // Prompt URL
    const promptUrl = `https://api.astria.ai/tunes/${tuneId}/prompts`;

    const promptData = new URLSearchParams({
      'prompt[text]': `<lora:${tuneId}:1.0> sks ${user} ${prompt}`,
      'prompt[num_images]': '1',
    });

    // 1. Post to generate prompt
    const promptResponse = await axios.post(promptUrl, promptData, {
      headers: {
        Authorization: `Bearer ${API_KEY}`,
      },
    });

    if (promptResponse.status !== 201) {
      return {
        success: false,
        message: `Astria prompt creation failed: ${JSON.stringify(
          promptResponse.data
        )}`,
      };
    }

    const promptDetails = promptResponse.data;
    const promptResultUrl = promptDetails.url;

    // 2. Poll until images are ready
    const imageUrls: string[] = [];
    while (true) {
      const resultResponse = await axios.get(promptResultUrl, {
        headers: { Authorization: `Bearer ${API_KEY}` },
      });
      const images = resultResponse.data.images || [];
      if (images.length > 0) {
        imageUrls.push(...images);
        break;
      }
      // Sleep 30 seconds
      await new Promise((resolve) => setTimeout(resolve, 30_000));
    }

    return {
      success: true,
      message: 'Images generated successfully',
      data: imageUrls,
    };
  } catch (error: any) {
    return {
      success: false,
      message: error?.message || 'Unknown error occurred',
    };
  }
}
