import SuggestFeature from '@/components/suggestFeature/SuggestFeature';
import { Metadata } from 'next';
import Script from 'next/script';

export const metadata: Metadata = {
  title: "Suggest a Feature - Help Improve Burst Mode | AI Photography Platform",
  description: "Share your ideas and suggestions to help us improve Burst Mode. Your feedback is valuable in shaping the future of our AI photography platform.",
  keywords: [
    "suggest feature",
    "Burst Mode feedback",
    "AI photography suggestions",
    "feature request",
    "product improvement",
    "Burst Mode ideas",
    "AI image generation feedback",
    "user suggestions",
    "product development",
    "feature ideas"
  ],
  openGraph: {
    title: "Suggest a Feature - Help Improve Burst Mode | AI Photography Platform",
    description: "Share your ideas and suggestions to help us improve Burst Mode. Your feedback is valuable in shaping the future of our AI photography platform.",
    url: "https://burstmode.ai/suggest-feature",
    siteName: "Burst Mode AI",
    images: [
      {
        url: "https://burstmode.ai/app/og-icon.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Photography Feature Suggestions",
      }
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Suggest a Feature - Help Improve Burst Mode | AI Photography Platform",
    description: "Share your ideas and suggestions to help us improve Burst Mode.",
    images: [
      {
        url: "https://burstmode.ai/app/og-icon.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Photography Feature Suggestions",
      }
    ],
  },
  alternates: {
    canonical: "https://burstmode.ai/suggest-feature",
  },
};

const SuggestFeaturePage = () => {
  return (
    <>
      {/* Structured Data for SEO */}
      <Script
        id="suggest-feature-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "Suggest a Feature - Help Improve Burst Mode",
            "description": "Share your ideas and suggestions to help us improve Burst Mode. Your feedback is valuable in shaping the future of our AI photography platform.",
            "url": "https://burstmode.ai/suggest-feature",
            "mainEntity": {
              "@type": "ContactPage",
              "name": "Feature Suggestion Form",
              "description": "Submit your feature ideas and suggestions to help improve Burst Mode AI.",
              "contactPoint": {
                "@type": "ContactPoint",
                "email": "<EMAIL>",
                "contactType": "customer support"
              }
            }
          })
        }}
      />
      <SuggestFeature />
    </>
  );
}

export default SuggestFeaturePage;
