export interface AstriaPack {
  id: number;
  slug: string;
  title: string;
  cover_url: string;
  created_at: string;
  updated_at: string;
  public_at: string | null;
  unlisted_at: string | null;
  disabled_at: string | null;
  costs: {
    [key: string]: {
      cost: number;
      num_images: number;
    };
  };
  logo: string | null;
  base_tune_id: number;
  token: string | null;
  model_type: string;
  preset: string;
  multiplier: number;
  prompt_likes_count: number;
  base_prompts_count: number | null;
  base_tunes_count: number | null;
}

export interface AstriaPacksResponse {
  success: boolean;
  message: string;
  data?: AstriaPack[];
  error?: string;
}

export interface AstriaPacksQueryParams {
  public?: boolean;
  listed?: boolean;
  gallery?: boolean;
}

// ---------------------------------------------------------
// Astria packTypes get by id
type Gender = "woman" | "man";

interface CostDetail {
  cost: number;
  num_images: number;
}

interface PromptImageSet {
  id: number;
  images: string[];
}

interface PromptsPerClass {
  woman: PromptImageSet[];
  man: PromptImageSet[];
}

export interface AstriaPackByIdTypes {
  id: number;
  slug: string;
  title: string;
  cover_url: string;
  created_at: string;
  updated_at: string;
  public_at: string | null;
  unlisted_at: string | null;
  disabled_at: string | null;
  costs: Record<Gender, CostDetail>;
  logo: string | null;
  base_tune_id: number;
  token: string | null;
  model_type: string;
  preset: string;
  multiplier: number;
  prompt_likes_count: number;
  base_prompts_count: number | null;
  base_tunes_count: number | null;
  prompts_per_class: PromptsPerClass;
}

export interface AstriaPackByIdResponse {
  success: boolean;
  message: string;
  data?: AstriaPackByIdTypes;
  error?: string;
}

// ---------------------------------------------------------
interface Tune {
  id: number;
  title: string;
}

export interface PromptData {
  id: number;
  callback: string | null;
  trained_at: string;
  started_training_at: string;
  created_at: string;
  updated_at: string;
  tune_id: number;
  prompt_likes_count: number;
  base_pack_id: number | null;
  input_image: string | null;
  mask_image: string | null;
  text: string;
  negative_prompt: string;
  cfg_scale: number | null;
  steps: number | null;
  super_resolution: boolean;
  ar: string;
  num_images: number;
  seed: number | null;
  controlnet_conditioning_scale: number | null;
  controlnet_txt2img: boolean;
  denoising_strength: number | null;
  style: string;
  w: number;
  h: number;
  url: string;
  images: string[];
  liked: boolean;
  tune: Tune;
  tunes: Tune[];
}

export interface AstriaPromptResponse {
  success: boolean;
  message: string;
  data?: PromptData;
  error?: string;
}
