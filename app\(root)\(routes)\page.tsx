import { HeroParallax } from "@/components/homePage/hero-parallax";
import HowItWorks from "@/components/homePage/HowItWorks";
import Product from "@/components/homePage/products/Product";
import { IMAGES } from "@/lib/constants";
import { ModalProvider } from "@/components/ui/animated-modal";
import Script from "next/script";
import { homePageSchema, organizationSchema } from "@/lib/structuredData";
import { PhotoPackGrid } from "@/components/photo-packs/photo-pack-grid";
import HomePagePricing from "@/components/home-page-pricing";

const RootPage = () => {
  return (
    <div className="">
      {/* Structured Data for SEO */}
      <Script
        id="homepage-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(homePageSchema) }}
      />
      <Script
        id="organization-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationSchema) }}
      />

      <HeroParallax products={IMAGES} />
      <ModalProvider>
        <HowItWorks />
      </ModalProvider>
      <Product />
      <PhotoPackGrid />
      <HomePagePricing />
    </div>
  );
};

export default RootPage;
