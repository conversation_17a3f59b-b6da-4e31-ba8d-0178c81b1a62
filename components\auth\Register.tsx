"use client";

import Link from "next/link";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { register, signInWithGoogle } from "@/lib/firebase"; //   Import Google Sign-In API
import { Poppins } from "next/font/google";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { FcGoogle } from "react-icons/fc"; //   Import Google Icon

const font = Poppins({
  weight: "600",
  subsets: ["latin"],
});

const RegisterForm = () => {
  const [username, setUsername] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { toast } = useToast();

  //   Handle Google Sign-In
  const handleGoogleSignIn = async () => {
    const response = await signInWithGoogle();

    if (!response.success) {
      toast({
        title: "Google Sign-In Failed",
        description: response.message,

      });
    } else {
      toast({
        title: "Sign-In Successful",
        description: "Welcome! Redirecting to your dashboard...",

      });
      router.push("/"); //   Redirect after successful login
    }
  };

  //   Handle Email/Password Registration
  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    const res = await register(email, username, password);
    if (!res.success) {
      toast({
        title: "Uh oh! Something went wrong.",
        description: res.message,
      });
    } else {
      toast({
        title: "Registration Successful",
        description: "Verification email sent. Check your email!",
      });
      router.push("/login");
    }
  };

  return (
    <div className="w-full max-w-md p-8 space-y-6">
      <div className="flex flex-col items-center justify-center space-y-2 text-center">
        <h1 className={cn("text-3xl font-bold", font.className)}>
          Create an Account
        </h1>
        <p className="text-sm text-muted-foreground">
          Enter your details to create your account
        </p>
      </div>
      <div className="p-6 bg-white/5 backdrop-blur-sm rounded-lg border border-primary/10 shadow-xl">

       {/* ✅ Google Sign-In Button */}
        <button
          onClick={handleGoogleSignIn}
          className="
            w-full flex items-center justify-center space-x-3
            py-3 px-6 border border-gray-300 bg-white text-gray-700
            rounded-lg shadow-md hover:bg-gray-100
            active:bg-gray-200 transition-all duration-300
            font-medium focus:outline-none focus:ring-2
            focus:ring-offset-2 focus:ring-gray-400
          "
        >
          <FcGoogle size={24} className="mr-2" />
          <span className="text-base">Continue with Google</span>
        </button>

        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-background text-muted-foreground">
              OR
            </span>
          </div>
        </div>

        {/*   Traditional Email Registration */}
        <form onSubmit={onSubmit} className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">
              Name
            </label>
            <input
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="w-full px-3 py-2 bg-secondary/50 border border-primary/10 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50 transition"
              required
              placeholder="John Doe"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">
              Email
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 bg-secondary/50 border border-primary/10 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50 transition"
              required
              placeholder="<EMAIL>"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">
              Password
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 bg-secondary/50 border border-primary/10 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50 transition"
              required
              placeholder="••••••••"
            />
          </div>
          {error && <p className="text-sm text-red-500">{error}</p>}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="terms"
                className="rounded border-primary/20"
                required
              />
              <label
                htmlFor="terms"
                className="text-sm text-muted-foreground cursor-pointer"
              >
                I agree to the{" "}
                <Link href="/terms" className="text-primary hover:underline">
                  Terms of Service
                </Link>{" "}
                and{" "}
                <Link href="/privacy" className="text-primary hover:underline">
                  Privacy Policy
                </Link>
              </label>
            </div>
          </div>
          <button
            type="submit"
            className="w-full py-2.5 px-4 bg-primary/10 text-white rounded-md hover:bg-primary/80 transition font-medium"
          >
            Create Account
          </button>
        </form>
      </div>
      <p className="text-center text-sm text-muted-foreground">
        Already have an account?{" "}
        <Link href="/login" className="text-primary hover:underline font-medium">
          Sign in
        </Link>
      </p>
    </div>
  );
};

export default RegisterForm;
