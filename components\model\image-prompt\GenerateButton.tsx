import React from "react";
import { Sparkles } from "lucide-react";
import { GenerateButtonProps } from "./types";
import { useUser } from "@/context/UserContext";
import { useToast } from "@/hooks/use-toast";
import { collection, doc, getDocs, updateDoc } from "@firebase/firestore";
import { db } from "@/lib/firebase";
import { generateImagesOnAstria, generateVideoOnAstria } from "@/lib/api";

export default function GenerateButton({
  isLoading,
  setIsLoading,
  prompt,
  enhancedPrompt,
  useEnhancedPrompt,
  isEnhancing,
  modelId,
  userId,
  enhancementType,
  setImageUrl,
  onStartProcessing,
  onCompleteProcessing,
  inputImage,
  controlnetType,
  denoisingStrength,
  style,
  garmentModelId,
  interiorOptions,
  videoMode = false,
  videoPrompt,
  videoModel,
  onVideoGenerated,
}: GenerateButtonProps) {
  const { imageCredits, decrementImageCredits } = useUser();
  const { toast } = useToast();

  const handleSubmit = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    setIsLoading(true);
    onStartProcessing();

    const subscribedPlanRef = collection(db, "users", userId, "subscribedPlan");
    const querySnapshot = await getDocs(subscribedPlanRef);

    if (querySnapshot.empty) {
      setIsLoading(false);
      onCompleteProcessing(); // Stop the processing UI
      toast({
        title: "⚠️ Subscription Issue",
        description: "No active subscription plan found. Please check your account settings.",
        variant: "destructive",
        duration: 5000,
      });
      return;
    }

    // Get the first document ID in `subscribedPlan`
    const planDoc = querySnapshot.docs[0];
    const planDocId = planDoc.id;
    const imageCredits = planDoc.data().planImageCredits;

    // Reference the document and decrement imageCredits
    const planRef = doc(db, "users", userId, "subscribedPlan", planDocId);
    await updateDoc(planRef, {
      planImageCredits: imageCredits - 1,
    });

    // Update the local state
    decrementImageCredits();

    try {
      // Use enhanced prompt if available and selected, otherwise use original prompt
      const promptToUse =
        enhancedPrompt && useEnhancedPrompt ? enhancedPrompt : prompt;

      // Prepare interior design specific parameters
      let interiorParams = {};
      if (interiorOptions) {
        // ControlNet parameters for interior design removed as they are not supported on Flux

        // Add masking parameters if enabled
        if (interiorOptions.useMasking) {
          interiorParams = {
            ...interiorParams,
            maskPrompt: interiorOptions.maskPrompt,
            maskInvert: interiorOptions.maskInvert,
          };
        }
      }

      // Handle video generation if videoMode is enabled
      if (videoMode) {
        const result = await generateVideoOnAstria({
          user: userId,
          tuneId: modelId,
          prompt: promptToUse,
          videoPrompt: videoPrompt || undefined,
          videoModel: videoModel || '720p',
          inputImage: inputImage ? (() => {
            const formData = new FormData();
            formData.append('image', inputImage);
            return formData;
          })() : null,
          controlnet: inputImage ? (controlnetType === 'mlsd' || controlnetType === 'segroom' ? undefined : controlnetType) : undefined,
          denoisingStrength: inputImage ? denoisingStrength : undefined,
          style: style || undefined,
        });

        if (!result.success) {
          toast({
            title: "⚠️ Video Generation Failed",
            description: result.message ?
              (typeof result.message === 'string' ? result.message : JSON.parse(result.message).error) :
              "There was an error generating your video. Please try again.",
            variant: "destructive",
            duration: 5000,
          });
          return;
        }

        if (result.data) {
          // Set the image URL
          setImageUrl(result.data.imageUrl);

          // Call the video callback if provided
          if (onVideoGenerated && result.data.videoUrl) {
            onVideoGenerated(result.data.videoUrl);
          }
        }
      } else {
        // Standard image generation
        const result = await generateImagesOnAstria({
          user: userId,
          tuneId: modelId,
          prompt: promptToUse,
          enhancementType: enhancementType,
          // Removed negative prompt as it's not supported on Flux
          inputImage: inputImage ? (() => {
            const formData = new FormData();
            formData.append('image', inputImage);
            return formData;
          })() : null,
          controlnet: inputImage ? (controlnetType === 'mlsd' || controlnetType === 'segroom' ? undefined : controlnetType) : undefined,
          denoisingStrength: inputImage ? denoisingStrength : undefined,
          style: style || undefined,
          garmentModelId: garmentModelId,
          ...interiorParams,
        });

        if (!result.success) {
          toast({
            title: "⚠️ Image Generation Failed",
            description: result.message ?
              (typeof result.message === 'string' ? result.message : JSON.parse(result.message).error) :
              "There was an error generating your image. Please try again.",
            variant: "destructive",
            duration: 5000,
          });
          return;
        }

        if (result.data?.length) {
          const redirectUrl = result.data[0];
          setImageUrl(redirectUrl);
        }
      }
    } catch (error) {
      toast({
        title: "⚠️ Process Failed",
        description:
          "There was an error processing your request. Please try again later.",
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
      onCompleteProcessing();
    }
  };

  return (
    <div className="pt-2">
      <button
        onClick={(e) => {
          if (imageCredits === 0) {
            e.preventDefault();
            toast({
              title: "⚠️ Out of Image Credits!",
              description:
                "You have 0 image credits left. Upgrade your plan to generate more images.",
              duration: 4000,
            });
            return;
          }
          handleSubmit(e);
        }}
        disabled={isLoading || !prompt.trim() || isEnhancing}
        title={inputImage ? "Using reference image" : "Text to image generation"}
        className={`
          w-full
          ${
            prompt.trim() && !isEnhancing
              ? "bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-500 hover:to-indigo-500"
              : "bg-gradient-to-r from-purple-600/40 to-indigo-600/40 cursor-not-allowed"
          }
          text-white
          py-3
          px-6
          rounded-lg
          transition-all
          duration-300
          ease-in-out
          flex items-center justify-center gap-2
          shadow-lg shadow-purple-900/20
        `}
      >
        {isLoading ? (
          <>
            <div className="h-5 w-5 animate-spin rounded-full border-b-2 border-white"></div>
            <span>Processing...</span>
          </>
        ) : (
          <>
            <Sparkles size={18} />
            <span>
              {videoMode
                ? (inputImage ? "Generate Video from Reference" : "Generate Video")
                : (inputImage ? "Generate from Reference" : "Generate Image")
              }{" "}
              {useEnhancedPrompt && enhancedPrompt
                ? "(with Enhanced Prompt)"
                : ""}
            </span>
          </>
        )}
      </button>
      <div className="text-white/60 text-xs mt-2 text-center">
        {imageCredits} image {imageCredits === 1 ? "credit" : "credits"}{" "}
        remaining
      </div>
    </div>
  );
}
