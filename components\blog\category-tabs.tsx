"use client";

import { Button } from "@/components/ui/Button";
import { scrollToWithOffset } from "@/utility/utility";
import { useRouter, useSearchParams } from "next/navigation";

interface CategoryTabsProps {
  categories: string[];
  activeCategory: string;
  onCategoryChange: (category: string) => void;
}

export default function CategoryTabs({
  categories,
  activeCategory,
  onCategoryChange,
}: CategoryTabsProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  // inside your CategoryTabs component
  const handleCategoryChange = (category: string) => {
    const newSearchParams = new URLSearchParams(searchParams.toString());
    newSearchParams.set("category", category);

    router.push(`?${newSearchParams.toString()}`, {
      scroll: false,
    });

    onCategoryChange(category);

    scrollToWithOffset("category-tabs", 70);
  };

  return (
    <div className="w-full sm:w-auto overflow-x-auto pb-2 sm:pb-0">
      <nav className="flex space-x-2" aria-label="Blog categories">
        {categories.map((category) => (
          <Button
            key={category}
            onClick={() => handleCategoryChange(category)}
            variant={activeCategory === category ? "default" : "outline"}
            size="sm"
            className="whitespace-nowrap"
            aria-current={activeCategory === category ? "page" : undefined}
          >
            {category}
          </Button>
        ))}
      </nav>
    </div>
  );
}
