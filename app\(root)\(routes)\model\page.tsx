import Model from "@/components/model/Model";
import React from "react";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "AI Image Enhancement Tools | Burst Mode AI",
  description: "Explore our range of AI-powered image enhancement tools including headshots, food photography, product shots, virtual try-on, interior design, and more.",
  openGraph: {
    title: "AI Image Enhancement Tools | Burst Mode AI",
    description: "Explore our range of AI-powered image enhancement tools including headshots, food photography, product shots, virtual try-on, interior design, and more.",
    type: "website",
  },
};

const ModelPage = () => {
  return <Model />;
};

export default ModelPage;
