import ContactUs from '@/components/contactUs/ContactUs';
import { Metadata } from 'next';
import Script from 'next/script';

export const metadata: Metadata = {
  title: "Contact Burst Mode - Get Support & Assistance | AI Photography Platform",
  description: "Have questions about Burst Mode's AI photography services? Contact our support team for assistance with your account, technical issues, or general inquiries.",
  keywords: [
    "contact Burst Mode",
    "Burst Mode support",
    "AI photography help",
    "Burst Mode customer service",
    "AI image generation support",
    "contact AI photography team",
    "Burst Mode technical support",
    "AI photo enhancement help"
  ],
  openGraph: {
    title: "Contact Burst Mode - Get Support & Assistance | AI Photography Platform",
    description: "Have questions about Burst Mode's AI photography services? Contact our support team for assistance with your account, technical issues, or general inquiries.",
    url: "https://burstmode.ai/contact-us",
    siteName: "Burst Mode AI",
    images: [
      {
        url: "https://burstmode.ai/app/og-icon.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Photography Support",
      }
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Contact Burst Mode - Get Support & Assistance | AI Photography Platform",
    description: "Have questions about Burst Mode's AI photography services? Contact our support team for assistance.",
    images: [
      {
        url: "https://burstmode.ai/app/og-icon.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Photography Support",
      }
    ],
  },
  alternates: {
    canonical: "https://burstmode.ai/contact-us",
  },
};

const ContactUsPage = () => {
  return (
    <>
      {/* Structured Data for SEO */}
      <Script
        id="contact-page-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "ContactPage",
            "name": "Contact Burst Mode",
            "description": "Contact our support team for assistance with your account, technical issues, or general inquiries.",
            "url": "https://burstmode.ai/contact-us",
            "mainEntity": {
              "@type": "Organization",
              "name": "Burst Mode AI",
              "url": "https://burstmode.ai",
              "logo": "https://burstmode.ai/app/icon-transparent.png",
              "contactPoint": {
                "@type": "ContactPoint",
                "email": "<EMAIL>",
                "contactType": "customer service",
                "availableLanguage": "English"
              }
            }
          })
        }}
      />
      <ContactUs />
    </>
  );
}

export default ContactUsPage;