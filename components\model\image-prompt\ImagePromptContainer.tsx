import React from "react";
import { ImagePromptProps, StyleType } from "./types";
import PresetPrompts from "./PresetPrompts";
import EnhancedPromptBox from "./EnhancedPromptBox";
import CustomPromptInput from "./CustomPromptInput";
// Negative prompt removed
import GenerateButton from "./GenerateButton";
import ImageDisplay from "./ImageDisplay";
import ImageUploader from "./ImageUploader";
import StyleSelector from "./StyleSelector";
import InteriorDesignOptions from "./InteriorDesignOptions";
import VideoOptions from "./VideoOptions";
import { InteriorDesignOptions as InteriorOptions } from "@/types/interiorDesign";
import { VideoDisplay } from "../video-prompt";
import { usePathname } from "next/navigation";

export default function ImagePromptContainer({
  modelId,
  userId,
  imageUrl,
  videoUrl,
  setImageUrl,
  onStartProcessing,
  onCompleteProcessing,
  enhancementType,
  garmentModelId,
  videoMode = false,
  onVideoGenerated,
}: ImagePromptProps) {
  const [prompt, setPrompt] = React.useState("");
  // Negative prompt removed
  const [enhancedPrompt, setEnhancedPrompt] = React.useState<string | null>(
    null
  );
  const [isEnhancing, setIsEnhancing] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);
  const [selectedPresetIndex, setSelectedPresetIndex] = React.useState<
    number | null
  >(null);
  const [useEnhancedPrompt, setUseEnhancedPrompt] = React.useState(false);

  // Image-to-image state
  const [inputImage, setInputImage] = React.useState<File | null>(null);
  const [inputImageUrl, setInputImageUrl] = React.useState<string | null>(null);
  const [controlnetType, setControlnetType] = React.useState<string | null>(
    null
  );
  const [denoisingStrength, setDenoisingStrength] = React.useState<number>(0.7);
  const [style, setStyle] = React.useState<StyleType>(null);

  // Interior design specific state
  const [interiorOptions, setInteriorOptions] = React.useState<InteriorOptions>(
    {
      useSegroom: false,
      useMlsd: true,
      controlnetWeights: {
        segroom: 1.0,
        mlsd: 0.5,
      },
      useMasking: false,
      maskPrompt: "windows door",
      maskInvert: true,
    }
  );

  // Video generation specific state
  const [videoPrompt, setVideoPrompt] = React.useState<string>("");
  const [videoModel, setVideoModel] = React.useState<"720p" | "480p">("720p");

  const pathname = usePathname();

  return (
    <div className="w-full h-full sm:p-4 md:p-6">
      <div
        className={`
        w-full
        h-full
        flex
        flex-col ${
          pathname.includes("image-to-video") ? "lg:flex-row" : "md:flex-row"
        }
        rounded-xl
        overflow-hidden
        bg-black/20
        backdrop-blur-md
        shadow-xl
        border border-white/5
      `}
      >
        {/* Left Column */}
        <div
          className={`
          flex flex-col
          w-full ${pathname.includes("image-to-video") ? "" : "md:w-"} 
          p-3 sm:p-6
          space-y-5
          overflow-y-auto
        `}
        >
          <PresetPrompts
            enhancementType={enhancementType}
            selectedPresetIndex={selectedPresetIndex}
            setSelectedPresetIndex={setSelectedPresetIndex}
            setPrompt={setPrompt}
            setEnhancedPrompt={setEnhancedPrompt}
            setUseEnhancedPrompt={setUseEnhancedPrompt}
          />

          {enhancedPrompt && (
            <EnhancedPromptBox
              enhancedPrompt={enhancedPrompt}
              useEnhancedPrompt={useEnhancedPrompt}
              setUseEnhancedPrompt={setUseEnhancedPrompt}
            />
          )}

          <CustomPromptInput
            prompt={prompt}
            setPrompt={setPrompt}
            isEnhancing={isEnhancing}
            setIsEnhancing={setIsEnhancing}
            enhancementType={enhancementType}
            selectedPresetIndex={selectedPresetIndex}
            setSelectedPresetIndex={setSelectedPresetIndex}
            setEnhancedPrompt={setEnhancedPrompt}
            setUseEnhancedPrompt={setUseEnhancedPrompt}
          />

          {/* Negative prompt removed */}

          <ImageUploader
            inputImage={inputImage}
            setInputImage={setInputImage}
            inputImageUrl={inputImageUrl}
            setInputImageUrl={setInputImageUrl}
            controlnetType={controlnetType}
            setControlnetType={setControlnetType}
            denoisingStrength={denoisingStrength}
            setDenoisingStrength={setDenoisingStrength}
            style={style}
            setStyle={setStyle}
          />

          <StyleSelector style={style} setStyle={setStyle} />

          {enhancementType === "interior" && (
            <InteriorDesignOptions
              options={interiorOptions}
              setOptions={setInteriorOptions}
            />
          )}

          {videoMode && (
            <VideoOptions
              videoPrompt={videoPrompt}
              setVideoPrompt={setVideoPrompt}
              videoModel={videoModel}
              setVideoModel={setVideoModel}
            />
          )}

          <GenerateButton
            isLoading={isLoading}
            setIsLoading={setIsLoading}
            prompt={prompt}
            // Removed negative prompt parameter as it's not supported on Flux
            enhancedPrompt={enhancedPrompt}
            useEnhancedPrompt={useEnhancedPrompt}
            isEnhancing={isEnhancing}
            modelId={modelId}
            userId={userId}
            enhancementType={enhancementType}
            setImageUrl={setImageUrl}
            onStartProcessing={onStartProcessing}
            onCompleteProcessing={onCompleteProcessing}
            inputImage={inputImage}
            controlnetType={controlnetType}
            denoisingStrength={denoisingStrength}
            style={style}
            garmentModelId={garmentModelId}
            interiorOptions={
              enhancementType === "interior" ? interiorOptions : undefined
            }
            videoMode={videoMode}
            videoPrompt={videoPrompt}
            videoModel={videoModel}
            onVideoGenerated={onVideoGenerated}
          />
        </div>

        {/* Right Column */}
        {!pathname.includes("image-to-video") ? (
          <ImageDisplay
            isLoading={isLoading}
            imageUrl={imageUrl as string | null}
            enhancementType={enhancementType}
            useEnhancedPrompt={useEnhancedPrompt}
            enhancedPrompt={enhancedPrompt}
          />
        ) : (
          <VideoDisplay videoUrl={videoUrl || null} />
        )}
      </div>
    </div>
  );
}
