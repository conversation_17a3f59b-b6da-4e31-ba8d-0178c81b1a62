"use client";

import React from "react";
import {
  Links,
  MobileSidebar,
  SidebarLink,
  useSidebar,
} from "@/components/ui/sidebar/Sidebar";
import {
  IconLogin2,
  IconHome,
  IconCalendarDollar,
  IconAi,
  IconLogout2,
} from "@tabler/icons-react";
import { Logo } from "@/components/ui/sidebar/Logo";
import { LogoIcon } from "@/components/ui/sidebar/LogoIcon";
import { useUser } from "@/context/UserContext";
import { getInitials } from "@/lib/utils";

const MobileSidebarWrapper = () => {
  const { open, setOpen } = useSidebar();
  const { user, logout } = useUser();

  const links = [
    {
      label: "Home",
      href: "/",
      icon: (
        <IconHome className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
      onClick: () => setOpen(false),
    },
    {
      label: "Tools",
      href: "/model",
      icon: (
        <IconAi className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
      onClick: () => setOpen(false),
    },
    {
      label: "Pricing",
      href: "/pricing",
      icon: (
        <IconCalendarDollar className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
      onClick: () => setOpen(false),
    },
    {
      label: "Blog",
      href: "/blog",
      icon: (
        <IconCalendarDollar className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
      onClick: () => setOpen(false),
    },
    {
      label: "Suggest Feature",
      href: "/suggest-feature",
      icon: (
        <IconCalendarDollar className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
      onClick: () => setOpen(false),
    },
    user && {
      label: "Logout",
      href: "/",
      icon: (
        <IconLogout2 className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
      onClick: () => {
        logout();
        setOpen(false);
      },
    },
    !user && {
      label: "Login",
      href: "/login",
      icon: (
        <IconLogin2 className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
      onClick: () => setOpen(false),
    },
  ].filter(Boolean) as Links[];

  return (
    <MobileSidebar>
      <div className="flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
        {open ? <Logo /> : <LogoIcon />}
        <div className="mt-8 flex flex-col gap-2">
          {links.map((link, idx) => (
            <SidebarLink key={idx} link={link} onClick={link.onClick} />
          ))}
        </div>
      </div>
      <div>
        {user && (
          <SidebarLink
            link={{
              label: user?.fullname ? user.fullname : "Guest",
              href: "#",
              icon: (
                <div className="relative inline-flex items-center justify-center w-10 h-10 overflow-hidden bg-purple-400 rounded-full dark:bg-purple-700 cursor-pointer hover:opacity-80">
                  <span className="font-medium text-gray-600 dark:text-gray-300">
                    {user ? getInitials(user.fullname) : "U"}
                  </span>
                </div>
              ),
            }}
          />
        )}
      </div>
    </MobileSidebar>
  );
};

export default MobileSidebarWrapper;
