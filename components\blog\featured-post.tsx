  import Image from "next/image";
  import Link from "next/link";
  import { ArrowRight } from "lucide-react";
  import type { Post } from "@/types/blogs";
  import { formatDate } from "@/utility/utility";
  import { Badge } from "@/components/ui/badge";
  import { Button } from "@/components/ui/Button";

  interface FeaturedPostProps {
    post: Post;
  }

  export default function FeaturedPost({ post }: FeaturedPostProps) {
    return (
      <article
        className="relative rounded-xl overflow-hidden group top-10"
        aria-labelledby={`featured-post-${post.id}`}
      >
        <div className="relative h-[400px] sm:h-[500px] w-full">
          <Image
            src={post.coverImage || "/placeholder.svg"}
            alt={post.title}
            fill
            className="object-cover transition-transform duration-500 group-hover:scale-105"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1200px"
            priority
          />
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-black/20" />
        </div>

        <div className="absolute bottom-0 left-0 right-0 p-6 sm:p-8 text-white">
          <div className="flex items-center gap-3 mb-3">
            <Badge
              variant="secondary"
              className="bg-primary/80 hover:bg-primary/70 text-white border-none"
            >
              {post.category}
            </Badge>
            <time dateTime={post.date} className="text-sm opacity-90">
              {formatDate(post.date)}
            </time>
            <span className="text-sm opacity-90">
              {post.readingTime} min read
            </span>
          </div>

          <h2
            id={`featured-post-${post.id}`}
            className="text-2xl sm:text-3xl md:text-4xl font-bold mb-3 leading-tight"
          >
            {post.title}
          </h2>

          <p className="text-base sm:text-lg opacity-90 mb-6 line-clamp-2 sm:line-clamp-3 max-w-3xl">
            {post.excerpt}
          </p>

          <Button
            asChild
            variant="outline"
            className="bg-white/10 hover:bg-white/20 text-white border-none"
          >
            <Link href={`/blog/${post.slug}`}>
              Read More <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </article>
    );
  }
