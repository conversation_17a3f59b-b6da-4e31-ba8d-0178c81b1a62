import { NextRequest } from "next/server";
import { addUserConnection, removeUserConnection } from "@/lib/socket";

export async function GET(req: NextRequest) {
  const url = new URL(req.url);
  const userId = url.searchParams.get("userId");

  if (!userId) {
    return new Response("User ID required", { status: 400 });
  }

  // Create SSE stream
  const stream = new ReadableStream({
    start(controller) {
      // Add connection to user-specific store
      addUserConnection(userId, controller);

      // Send initial connection message
      controller.enqueue(new TextEncoder().encode("data: {\"type\":\"connected\"}\n\n"));

      // Handle cleanup when connection closes
      req.signal.addEventListener('abort', () => {
        removeUserConnection(userId, controller);
        controller.close();
      });
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  });
}
