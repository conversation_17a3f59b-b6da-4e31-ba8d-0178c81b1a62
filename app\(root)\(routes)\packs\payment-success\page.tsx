"use client";

import { Suspense } from "react";
import PaymentSuccessContent from "./PaymentSuccessContent";

export default function PaymentSuccess() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-background via-background to-secondary/20">
        <div className="animate-pulse">
          <div className="w-16 h-16 bg-primary/20 rounded-full mb-4"></div>
          <div className="w-32 h-4 bg-primary/20 rounded"></div>
        </div>
      </div>
    }>
      <PaymentSuccessContent />
    </Suspense>
  );
}
