import BlogSection from "@/components/blog/blog-section";
import { blogPosts } from "@/types/data";
import { Metadata } from "next";
import Script from 'next/script';
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "Blog | Burst Mode AI Photography",
  description: "Explore the latest articles on AI photography, image enhancement techniques, camera technology, and creative tips from Burst Mode's expert team.",
  keywords: [
    "AI photography blog",
    "image enhancement tips",
    "photography tutorials",
    "camera technology",
    "Burst Mode blog",
    "AI image generation",
    "photo editing guides",
    "photography techniques",
    "digital photography blog",
    "AI photography trends"
  ],
  openGraph: {
    title: "Blog | Burst Mode AI Photography",
    description: "Explore the latest articles on AI photography, image enhancement techniques, camera technology, and creative tips from Burst Mode's expert team.",
    type: "website",
    images: [
      {
        url: "https://burstmode.ai/app/og-icon.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Photography Blog",
      }
    ],
    url: "https://burstmode.ai/blog",
    siteName: "Burst Mode AI",
  },
  twitter: {
    card: "summary_large_image",
    title: "Blog | Burst Mode AI Photography",
    description: "Explore the latest articles on AI photography, image enhancement techniques, camera technology, and creative tips.",
    images: [
      {
        url: "https://burstmode.ai/app/og-icon.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Photography Blog",
      }
    ],
    creator: "@burstmodeai",
    site: "@burstmodeai",
  },
  alternates: {
    canonical: "https://burstmode.ai/blog",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function Blogs() {
  return (
    <>
      <Script
        id="blog-page-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Blog",
            "name": "Burst Mode AI Photography Blog",
            "description": "Explore the latest articles on AI photography, image enhancement techniques, camera technology, and creative tips from Burst Mode's expert team.",
            "url": "https://burstmode.ai/blog",
            "publisher": {
              "@type": "Organization",
              "name": "Burst Mode AI",
              "url": "https://burstmode.ai",
              "logo": {
                "@type": "ImageObject",
                "url": "https://burstmode.ai/app/icon.png"
              }
            },
            "blogPost": blogPosts.slice(0, 10).map(post => ({
              "@type": "BlogPosting",
              "headline": post.title,
              "description": post.excerpt,
              "datePublished": post.date,
              "image": post.coverImage || "/placeholder.svg",
              "url": `https://burstmode.ai/blog/${post.slug}`
            }))
          })
        }}
      />
      <Suspense fallback={<div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Loading Blog...</h2>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
        </div>
      </div>}>
        <BlogSection posts={blogPosts} />
      </Suspense>
    </>
  );
}
