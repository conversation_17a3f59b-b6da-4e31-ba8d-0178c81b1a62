import React from "react";
import { ImagePromptContainer } from "./image-prompt";

interface ImageToVideoPromptProps {
  modelId: string; // The Astria tuneId
  userId: string; // Firebase user.uid
  title: string; // The selected (or new) model title
  imageUrl: string | null;
  setImageUrl: React.Dispatch<React.SetStateAction<string | null>>;
  videoUrl: string | null;
  setVideoUrl: React.Dispatch<React.SetStateAction<string | null>>;
  onStartProcessing: () => void;
  onCompleteProcessing: () => void;
  enhancementType: string;
}

const ImageToVideoPrompt: React.FC<ImageToVideoPromptProps> = ({
  modelId,
  userId,
  title,
  imageUrl,
  setImageUrl,
  videoUrl,
  setVideoUrl,
  onStartProcessing,
  onCompleteProcessing,
  enhancementType,
}) => {
  // Handle image generation result
  const handleImageGenerated = (url: string) => {
    setImageUrl(url);
  };

  // Handle video generation result
  const handleVideoGenerated = (url: string) => {
    setVideoUrl(url);
  };

  return (
    <div className="space-y-6">
      <div className=" p-6 rounded-lg shadow-lg">
        <h2 className="text-xl font-semibold text-white mb-4">
          Generate Video from Image
        </h2>
        <p className="text-gray-400 mb-4">
          First, generate an image with your prompt, then convert it to a video.
        </p>

        <ImagePromptContainer
          modelId={modelId}
          userId={userId}
          title={title}
          imageUrl={imageUrl}
          videoUrl={videoUrl}
          setImageUrl={setImageUrl}
          onStartProcessing={onStartProcessing}
          onCompleteProcessing={onCompleteProcessing}
          enhancementType={enhancementType}
          videoMode={true}
          onVideoGenerated={handleVideoGenerated}
        />
      </div>
    </div>
  );
};

export default ImageToVideoPrompt;
