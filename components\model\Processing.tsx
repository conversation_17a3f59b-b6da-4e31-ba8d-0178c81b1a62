interface ProcessingProps {
  message: string;
}

export default function Processing({ message }: ProcessingProps) {
  return (
    <div className="bg-amber-50/10 backdrop-blur-sm border border-amber-200/20 p-6 rounded-2xl">
      <div className="flex items-center gap-3">
        <div className="animate-spin h-5 w-5 border-2 border-amber-500 border-t-transparent rounded-full" />
        <p className="text-amber-500 font-medium">
          {message}
        </p>
      </div>
    </div>
  );
}