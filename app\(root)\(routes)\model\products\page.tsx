import React from "react";
import EnhancementWrapper from "@/components/model/EnhancementWrapper";
import { Metadata } from 'next';
import Script from 'next/script';

export const metadata: Metadata = {
  title: "AI Product Photography Enhancement | Burst Mode",
  description: "Transform your product photos with Burst Mode's AI enhancement. Create professional, high-converting product images for e-commerce, marketing materials, and social media.",
  keywords: [
    "AI product photography",
    "product photo enhancement",
    "e-commerce product images",
    "product marketing photos",
    "professional product photography",
    "product image optimization",
    "AI product image generation",
    "e-commerce photography",
    "product listing photos",
    "product visualization AI"
  ],
  openGraph: {
    title: "AI Product Photography Enhancement | Burst Mode",
    description: "Transform your product photos with Burst Mode's AI enhancement. Create professional, high-converting product images for e-commerce, marketing materials, and social media.",
    url: "https://burstmode.ai/model/products",
    siteName: "Burst Mode AI",
    images: [
      {
        url: "https://burstmode.ai/og-product_2.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Product Photography",
      }
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "AI Product Photography Enhancement | Burst Mode",
    description: "Transform your product photos with Burst Mode's AI enhancement. Create professional, high-converting product images.",
    images: [
      {
        url: "https://burstmode.ai/og-product_2.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Product Photography",
      }
    ],
  },
  alternates: {
    canonical: "https://burstmode.ai/model/products",
  },
};

const ProductEnhancementPage = () => {
  return (
    <>
      {/* Structured Data for SEO */}
      <Script
        id="product-enhancement-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "AI Product Photography Enhancement",
            "description": "Transform your product photos with Burst Mode's AI enhancement. Create professional, high-converting product images for e-commerce, marketing materials, and social media.",
            "url": "https://burstmode.ai/model/products",
            "mainEntity": {
              "@type": "Service",
              "name": "AI Product Photography Enhancement",
              "provider": {
                "@type": "Organization",
                "name": "Burst Mode AI",
                "url": "https://burstmode.ai"
              },
              "description": "Our AI-powered product photography enhancement service transforms ordinary product photos into stunning, professional-quality images. Perfect for e-commerce stores, marketplaces, and marketing materials.",
              "offers": {
                "@type": "Offer",
                "price": "15.00",
                "priceCurrency": "USD"
              },
              "serviceType": "AI Photography Enhancement"
            }
          })
        }}
      />
      <EnhancementWrapper enhancementType="product" />
    </>
  );
};

export default ProductEnhancementPage;