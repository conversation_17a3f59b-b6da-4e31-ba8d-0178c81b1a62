'use client';

import {
  Accordion,
  AccordionItem,
} from "@nextui-org/accordion";

const FAQPage = () => {
  const faqs = [
    {
      question: "What types of photos can I generate and enhance with Burst Mode?",
      answer: ["Headshots", "Product photos", "Food photos"],
    },
    {
      question: "How does Burst Mode enhance my photos?",
      answer: [
        "Skin smoothing: Reduces blemishes and wrinkles for natural-looking skin.",
        "Eye enhancement: Brightens eyes and removes any redness.",
        "Color correction: Adjusts color balance and enhances vibrancy.",
        "Background refinement: Improves background quality and removes distractions.",
        "Sharpness and clarity: Increases sharpness and clarity for sharper images.",
      ],
    },
    {
      question: "Is Burst Mode easy to use?",
      answer: "Absolutely! Our platform is designed to be user-friendly and accessible to everyone. Simply upload your photos, and our AI will do the rest.",
    },
    {
      question: "What are the system requirements?",
      answer: [
        "A stable internet connection.",
        "A modern web browser (Chrome, Firefox, Safari, Edge).",
      ],
    },
    {
      question: "How much does Burst Mode cost?",
      answer: "Please see our Pricing section.",
    },
    {
      question: "What is your privacy policy?",
      answer: "We are committed to protecting your privacy. Please refer to our Privacy Policy page for detailed information on how we collect, use, and protect your data.",
    },
    {
      question: "How can I contact support?",
      answer: "You can contact our support team by writing <NAME_EMAIL> or by using the contact form on our Contact Us page.",
    },
    {
      question: "Does Burst Mode offer a free trial?",
      answer: "No, we do not offer a free trial at this time, but please be on the lookout for any promotional offers.",
    },
    {
      question: "Can I cancel my subscription at any time?",
      answer: "Yes, you can cancel your subscription at any time.",
    },
    {
      question: "Is my payment information secure?",
      answer: "Yes, all payments are processed securely through our payment gateway.",
    },
  ];  

  return (
    <div className="min-h-screen w-full flex flex-col items-center py-24 px-4">
      <div className="w-full max-w-3xl space-y-12">
        <div className="text-center space-y-4">
          <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            Frequently Asked Questions
          </h1>
        </div>

        <Accordion variant="splitted">
          {faqs.map((faq, index) => (
            <AccordionItem
              key={index}
              aria-label={faq.question}
              title={
                <h3 className="text-lg font-semibold">{faq.question}</h3>
              }
              className="mb-4"
            >
              <div className="py-2 text-muted-foreground">
                {Array.isArray(faq.answer) ? (
                  <ul className="space-y-3">
                    {faq.answer.map((item, i) => (
                      <li 
                        key={i} 
                        className="flex items-start gap-3 pl-2"
                      >
                        <span className="mt-2 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-primary/70" />
                        <span className="text-sm leading-relaxed">
                          {item}
                        </span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-sm leading-relaxed pl-2">
                    {faq.answer}
                  </p>
                )}
              </div>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </div>
  );
};

export default FAQPage;
