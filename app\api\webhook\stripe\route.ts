import { headers } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import { adminDb } from "@/lib/firebase/adminConfig";
import { PLANS } from "@/lib/constants";
import { sendMonthlyCreditResetEmail } from "@/lib/sendMonthlyCreditResetEmail";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: "2025-05-28.basil",
});

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET as string;

export async function POST(req: NextRequest) {
  const body = await req.text();
  const signature = (await headers()).get("stripe-signature");

  if (!signature) {
    console.error("No Stripe signature header found.");
    return NextResponse.json(
      { message: "No Stripe signature header" },
      { status: 400 }
    );
  }

  let event;

  try {
    event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
  } catch (err: any) {
    console.error("Webhook signature verification failed:", err.message);
    return NextResponse.json(
      { message: `Webhook Error: ${err.message}` },
      { status: 400 }
    );
  }

  switch (event.type) {
    case "invoice.payment_succeeded":
      const invoiceUpdate = event.data.object as Stripe.Invoice;
      if (invoiceUpdate.billing_reason === "subscription_cycle") {
        await updateUserByStripeCustomerId(
          invoiceUpdate.customer as string,
          invoiceUpdate.lines.data[0].pricing?.price_details?.price as string
        );
      }
      break;
    case "customer.subscription.deleted":
      const subscriptionDeleted = event.data.object as Stripe.Subscription;
      console.log(
        "Stripe Event: customer.subscription.deleted",
        subscriptionDeleted.id
      );
      try {
        const customerId = subscriptionDeleted.customer as string;
        const users = await adminDb
          .collection("users")
          .where("stripeId", "==", customerId)
          .limit(1)
          .get();

        if (!users.empty) {
          const userDoc = users.docs[0];
          const subscribedPlanCollection =
            userDoc.ref.collection("subscribedPlan");
          const planSnapshot = await subscribedPlanCollection.limit(1).get();

          if (!planSnapshot.empty) {
            const planDoc = planSnapshot.docs[0];
            await planDoc.ref.delete();
            console.log(
              `Deleted subscription plan doc ${planDoc.id} for user ${userDoc.id}`
            );
          } else {
            console.log(
              `No subscribedPlan document found for user ${userDoc.id} to delete.`
            );
          }
        } else {
          console.log(
            `No user found with Stripe ID: ${customerId} to delete subscription.`
          );
        }
      } catch (error) {
        console.error("Error deleting subscription:", error);
      }
      break;

    default:
      console.log("Unhandled event type:", event.type);
      break;
  }

  return NextResponse.json({ received: true }, { status: 200 });
}

async function updateUserByStripeCustomerId(
  customerId: string,
  planId: string
): Promise<void> {
  try {
    const plan = PLANS.find(
      (plan) => plan.priceId === planId || plan.yearlyPriceId === planId
    );

    if (!plan) {
      console.error(`Plan not found for planId: ${planId}`);
      return;
    }

    const users = await adminDb
      .collection("users")
      .where("stripeId", "==", customerId)
      .limit(1)
      .get();

    if (!users.empty) {
      const userDoc = users.docs[0];
      const subscribedPlanCollection = userDoc.ref.collection("subscribedPlan");
      const planSnapshot = await subscribedPlanCollection.limit(1).get();
      await userDoc.ref.update({ lowCreditEmailSent: false });
      if (!planSnapshot.empty) {
        const planDoc = planSnapshot.docs[0];

        await sendMonthlyCreditResetEmail(
          userDoc.data().fullname,
          planDoc.data().planName,
          Number(plan.imageCredits),
          new Date().toDateString(),
          Number(planDoc.data().planImageCredits),
          userDoc.data().email
        );

        await planDoc.ref.update({
          planCredits: plan.credit,
          planImageCredits: plan.imageCredits,
        });

        console.log(
          `Updated subscription plan doc ${planDoc.id} for user ${userDoc.id}`
        );
      } else {
        console.log(
          `No subscribedPlan document found for user ${userDoc.id} to update.`
        );
      }
    } else {
      console.log(`No user found with Stripe ID: ${customerId}`);
    }
  } catch (error) {
    console.error("Error updating user plan:", error);
  }
}
