import React from "react";
import ImageToVideoWrapper from "@/components/model/ImageToVideoWrapper";
import { Metadata } from 'next';
import <PERSON>ript from 'next/script';

export const metadata: Metadata = {
  title: "AI Image to Video Conversion | Burst Mode",
  description: "Transform your static images into dynamic videos with Burst Mode's AI technology. Create stunning motion content for social media, presentations, and marketing materials.",
  keywords: [
    "AI video generation",
    "image to video conversion",
    "AI motion graphics",
    "static image animation",
    "video content creation",
    "AI video transformation",
    "animated content generation",
    "video from image AI",
    "motion content creation",
    "AI video effects"
  ],
  openGraph: {
    title: "AI Image to Video Conversion | Burst Mode",
    description: "Transform your static images into dynamic videos with Burst Mode's AI technology. Create stunning motion content for social media, presentations, and marketing materials.",
    url: "https://burstmode.ai/model/image-to-video",
    siteName: "Burst Mode AI",
    images: [
      {
        url: "https://burstmode.ai/og-img2video.mp4",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Image to Video Conversion",
      }
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "AI Image to Video Conversion | Burst Mode",
    description: "Transform your static images into dynamic videos with Burst Mode's AI technology.",
    images: [
      {
        url: "https://burstmode.ai/og-img2video.mp4",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Image to Video Conversion",
      }
    ],
  },
  alternates: {
    canonical: "https://burstmode.ai/model/image-to-video",
  },
};

const ImageToVideoPage = () => {
  return (
    <>
      {/* Structured Data for SEO */}
      <Script
        id="image-to-video-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "AI Image to Video Conversion",
            "description": "Transform your static images into dynamic videos with Burst Mode's AI technology. Create stunning motion content for social media, presentations, and marketing materials.",
            "url": "https://burstmode.ai/model/image-to-video",
            "mainEntity": {
              "@type": "Service",
              "name": "AI Image to Video Conversion",
              "provider": {
                "@type": "Organization",
                "name": "Burst Mode AI",
                "url": "https://burstmode.ai"
              },
              "description": "Convert static images into dynamic videos using advanced AI technology",
              "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "USD"
              }
            }
          })
        }}
      />
      <ImageToVideoWrapper enhancementType="image-to-video" />
    </>
  );
};

export default ImageToVideoPage;
