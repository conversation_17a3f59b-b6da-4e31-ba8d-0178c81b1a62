"use client";
import Image from "next/image";
import Link from "next/link";
import { ArrowLeft, Share2 } from "lucide-react";
import { blogPosts } from "@/types/data";
import { formatDate } from "@/utility/utility";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/badge";

interface MagazineBlogPostVariantProps {
  slug: string;
}

export default function MagazineBlogPostVariant({
  slug,
}: MagazineBlogPostVariantProps) {
  // Find the blog post by slug
  const post = blogPosts.find((post) => post.slug === slug);

  // If post not found, show a fallback
  if (!post) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center p-8">
          <h1 className="text-3xl font-bold mb-4">Blog Post Not Found</h1>
          <p className="mb-6">
            The blog post you're looking for doesn't exist or has been moved.
          </p>
          <Link href="/blog">
            <Button>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Blog
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <main className="min-h-screen">
      {/* Hero Section */}
      <div className="relative h-[50vh] md:h-[70vh] w-full">
        <Image
          src={post.coverImage || "/placeholder.svg"}
          alt={post.title}
          fill
          className="object-cover"
          priority
          sizes="100vw"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent" />
        <div className="absolute bottom-0 left-0 right-0 p-6 md:p-12 text-white max-w-5xl mx-auto">
          <Badge
            variant="secondary"
            className="bg-white/20 hover:bg-white/30 border-none mb-4"
          >
            {post.category}
          </Badge>
          <h1 className="text-4xl md:text-6xl font-bold tracking-tight mb-4 leading-tight">
            {post.title}
          </h1>
          <p className="text-xl md:text-2xl text-white/80 mb-6 max-w-3xl">
            {post.excerpt}
          </p>
          <div className="flex items-center text-sm text-white/70 space-x-4">
            <time dateTime={post.date}>{formatDate(post.date)}</time>
            <span>•</span>
            <span>{post.readingTime} min read</span>
          </div>
        </div>
      </div>

      {/* Article Content */}
      <article className="max-w-4xl mx-auto px-4 sm:px-6 py-12">
        <div className="flex justify-between items-center mb-12">
          <Link
            href="/blog"
            className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to all posts
          </Link>
          <Button
            variant="outline"
            size="sm"
            className="rounded-full"
            onClick={() => {
              if (navigator.share) {
                navigator
                  .share({
                    title: post.title,
                    text: post.excerpt,
                    url: window.location.href,
                  })
                  .catch((error) => console.log("Error sharing", error));
              } else {
                // Fallback for browsers that don't support navigator.share
                navigator.clipboard
                  .writeText(window.location.href)
                  .then(() => alert("Link copied to clipboard!"))
                  .catch((error) =>
                    console.log("Error copying to clipboard", error)
                  );
              }
            }}
          >
            <Share2 className="h-4 w-4 mr-2" /> Share
          </Button>
        </div>

        <div className="prose prose-lg max-w-none">
          <p className="lead text-xl md:text-2xl font-medium mb-8">
            {post.excerpt}
          </p>

          {/* Display the post content */}
          <div dangerouslySetInnerHTML={{ __html: post.content }} />
        </div>

        {/* Related Posts Section */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <h3 className="text-2xl font-bold mb-6">Related Articles</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {blogPosts
              .filter((p) => p.category === post.category && p.id !== post.id)
              .slice(0, 2)
              .map((relatedPost) => (
                <div
                  key={relatedPost.id}
                  className="border border-gray-200 rounded-lg overflow-hidden"
                >
                  <div className="relative h-48 w-full">
                    <Image
                      src={relatedPost.coverImage || "/placeholder.svg"}
                      alt={relatedPost.title}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, 50vw"
                    />
                  </div>
                  <div className="p-4">
                    <h4 className="font-bold text-lg mb-2">
                      {relatedPost.title}
                    </h4>
                    <p className="text-gray-600 text-sm mb-2">
                      {relatedPost.excerpt.substring(0, 100)}...
                    </p>
                    <Link
                      href={`/blog/${relatedPost.slug}`}
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    >
                      Read more →
                    </Link>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </article>
    </main>
  );
}
