import React from "react";
import { useRouter } from "next/navigation";
import ImageContent from "@/components/homePage/products/ImageContent";
import TextContent from "@/components/homePage/products/TextContent";

const ProductSection = ({
  title,
  description,
  testimonial,
  image,
  route,
  reversed,
}: {
  title: string;
  description: string;
  testimonial: {
    quote: string;
    author: string;
    role: string;
    avatar: string;
  };
  image: string;
  route: string;
  reversed: boolean;
}) => {
  return (
    <div
      className={`
      p-2 md:p-8 lg:p-12 
      rounded-3xl 
      ${reversed ? "bg-secondary/5" : "bg-primary/5"} 
      transition-all duration-300 hover:shadow-lg
      grid lg:grid-cols-2 gap-12 items-center
    `}
    >
      {reversed ? (
        <>
          <ImageContent reversed={reversed} image={image} />
          <TextContent
            title={title}
            description={description}
            testimonial={testimonial}
            image={image}
            route={route}
          />
        </>
      ) : (
        <>
          <TextContent
            title={title}
            description={description}
            testimonial={testimonial}
            image={image}
            route={route}
          />
          <ImageContent reversed={reversed} image={image} />
        </>
      )}
    </div>
  );
};

export default ProductSection;
