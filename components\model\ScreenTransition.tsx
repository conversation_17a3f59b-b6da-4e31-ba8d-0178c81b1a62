"use client";

import React, { ReactNode } from "react";
import { motion } from "framer-motion";

interface ScreenTransitionProps {
  children: ReactNode;
  screenKey: string;
  direction?: "left" | "right";
}

const ScreenTransition: React.FC<ScreenTransitionProps> = ({ 
  children, 
  screenKey,
  direction = "left" 
}) => {
  const xOffset = direction === "left" ? -20 : 20;
  
  return (
    <motion.div
      key={screenKey}
      initial={{ opacity: 0, x: xOffset }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: xOffset }}
    >
      {children}
    </motion.div>
  );
};

export default ScreenTransition;
