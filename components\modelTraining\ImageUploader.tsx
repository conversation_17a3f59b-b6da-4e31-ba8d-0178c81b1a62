import React, { useState, useCallback, useEffect } from "react";
import { Upload, X, Plus, AlertCircle, Info } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface ImageUploaderProps {
  images: (File | null)[];
  setImages: React.Dispatch<React.SetStateAction<(File | null)[]>>;
}

const ImageUploader = React.memo(
  ({ images, setImages }: ImageUploaderProps) => {
    const [isDragging, setIsDragging] = useState(false);
    const [isUploading, setIsUploading] = useState(false);
    const [imageUrls, setImageUrls] = useState<Record<number, string>>({});
    const [tooltipOpen, setTooltipOpen] = useState(false);
    const { toast } = useToast();

    // Auto-show tooltip when component mounts
    useEffect(() => {
      setTimeout(() => {
        setTooltipOpen(true);
      }, 1000);
      const timer = setTimeout(() => {
        setTooltipOpen(false);
      }, 4000);

      // Clean up timer on component unmount
      return () => clearTimeout(timer);
    }, []);

    // Create stable URLs for images to prevent re-renders
    React.useEffect(() => {
      const newUrls: Record<number, string> = {};
      images.forEach((img, index) => {
        if (img && !imageUrls[index]) {
          newUrls[index] = URL.createObjectURL(img);
        }
      });

      if (Object.keys(newUrls).length > 0) {
        setImageUrls((prev) => ({ ...prev, ...newUrls }));
      }

      // Cleanup function to revoke object URLs
      return () => {
        Object.values(imageUrls).forEach((url) => URL.revokeObjectURL(url));
      };
    }, [images]);

    // Check if adding this file would exceed the size limit
    const checkImageSizeLimit = useCallback(
      (file: File, currentIndex: number | null = null) => {
        const MAX_SIZE_MB = 4.5;
        const fileSizeMB = file.size / (1024 * 1024);

        // Check individual file size
        if (fileSizeMB > MAX_SIZE_MB) {
          toast({
            title: "Image Exceeds Size Limit",
            description: `This image is ${fileSizeMB.toFixed(
              2
            )}MB. Maximum allowed is ${MAX_SIZE_MB}MB per image.`,
            duration: 4000,
          });
          return false;
        }

        // Calculate total size if we add this file
        let totalSizeMB = 0;
        const validFiles = images.filter(
          (img, idx) =>
            img !== null && (currentIndex === null || idx !== currentIndex)
        ) as File[];
        totalSizeMB =
          validFiles.reduce((total, img) => total + img.size, 0) /
          (1024 * 1024);
        totalSizeMB += fileSizeMB;

        if (totalSizeMB > MAX_SIZE_MB) {
          toast({
            title: "Combined Size Exceeds Limit",
            description: `Total size would be ${totalSizeMB.toFixed(
              2
            )}MB. Maximum allowed is ${MAX_SIZE_MB}MB combined.`,
            duration: 4000,
          });
          return false;
        }

        return true;
      },
      [images, toast]
    );

    // Process multiple files and distribute them to placeholders
    const processMultipleFiles = useCallback(
      (files: File[]) => {
        try {
          setIsUploading(true);

          // Get current empty slots
          const newImages = [...images];
          const emptySlotIndices = newImages
            .map((img, idx) => (img === null ? idx : -1))
            .filter((idx) => idx !== -1);

          // Count how many files we can add (limited by empty slots and max total of 10)
          const availableSlots = Math.min(
            emptySlotIndices.length,
            files.length,
            10 - newImages.filter((img) => img !== null).length
          );

          // Process each file up to the available slot count
          for (let i = 0; i < availableSlots; i++) {
            const file = files[i];
            const slotIndex = emptySlotIndices[i];

            // Check file size limits
            if (!checkImageSizeLimit(file, slotIndex)) {
              continue;
            }

            // Add file to the appropriate slot
            newImages[slotIndex] = file;
          }

          // If all slots are filled and we have less than 10 images, add another slot
          const remainingEmptySlots = newImages.filter(
            (img) => img === null
          ).length;
          if (remainingEmptySlots === 0 && newImages.length < 10) {
            newImages.push(null);
          }

          setImages(newImages);

          // Show toast if some images couldn't be added
          if (files.length > availableSlots) {
            toast({
              title: "Some images not added",
              description: `Only ${availableSlots} out of ${files.length} images were added. Maximum is 10 images total.`,
              duration: 3000,
            });
          }
        } catch (error) {
          console.error("Error processing multiple images:", error);
          toast({
            title: "Upload Error",
            description:
              "There was an error uploading your images. Please try again.",
            duration: 3000,
          });
        } finally {
          setIsUploading(false);
        }
      },
      [images, toast, checkImageSizeLimit, setImages]
    );

    // Handle image changes (browse)
    const handleImageChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
        try {
          setIsUploading(true);
          const files = e.target.files;

          if (!files || files.length === 0) {
            setIsUploading(false);
            return;
          }

          // If multiple files are selected, process them all
          if (files.length > 1) {
            processMultipleFiles(Array.from(files));
            return;
          }

          // Single file handling (original behavior)
          const file = files[0];
          if (file) {
            // Check file size limits
            if (!checkImageSizeLimit(file, index)) {
              return;
            }

            const newImages = [...images];
            newImages[index] = file;

            // If this was the last empty slot and we have less than 10 images, add another slot
            const emptySlots = newImages.filter((img) => img === null).length;

            if (emptySlots === 0 && newImages.length < 10) {
              newImages.push(null);
            }

            setImages(newImages);
          }
        } catch (error) {
          console.error("Error uploading image:", error);
          toast({
            title: "Upload Error",
            description:
              "There was an error uploading your image. Please try again.",
            duration: 3000,
          });
        } finally {
          setIsUploading(false);
        }
      },
      [images, toast, checkImageSizeLimit, setImages]
    );

    // Drag-and-drop
    const handleDrop = useCallback(
      (e: React.DragEvent, index: number) => {
        try {
          e.preventDefault();
          setIsDragging(false);
          setIsUploading(true);

          // Get all dropped files
          const files = Array.from(e.dataTransfer.files).filter((file) =>
            file.type.startsWith("image/")
          );

          if (files.length === 0) {
            setIsUploading(false);
            return;
          }

          // If multiple files are dropped, process them all
          if (files.length > 1) {
            processMultipleFiles(files);
            return;
          }

          // Single file handling (original behavior)
          const file = files[0];
          if (file) {
            // Check file size limits
            if (!checkImageSizeLimit(file, index)) {
              return;
            }

            const newImages = [...images];
            newImages[index] = file;

            // If this was the last empty slot and we have less than 10 images, add another slot
            const emptySlots = newImages.filter((img) => img === null).length;

            if (emptySlots === 0 && newImages.length < 10) {
              newImages.push(null);
            }

            setImages(newImages);
          }
        } catch (error) {
          console.error("Error uploading image:", error);
          toast({
            title: "Upload Error",
            description:
              "There was an error uploading your image. Please try again.",
            duration: 3000,
          });
        } finally {
          setIsUploading(false);
        }
      },
      [images, toast, checkImageSizeLimit, setImages, processMultipleFiles]
    );

    const removeImage = useCallback(
      (index: number) => {
        const newImages = [...images];
        newImages[index] = null;
        setImages(newImages);
      },
      [images, setImages]
    );

    // Add one more image slot (up to 10)
    const addMoreImageSlot = useCallback(() => {
      // Add one more empty slot if we have less than 10 total
      if (images.length < 10) {
        const newImages = [...images];
        // Add just one more slot
        newImages.push(null);
        setImages(newImages);
      }
    }, [images, setImages]);

    return (
      <>
        {/* Brown info box with tooltip on hover - styled to match the image */}
        <TooltipProvider>
          <Tooltip open={tooltipOpen} onOpenChange={setTooltipOpen}>
            <TooltipTrigger asChild>
              <div className="flex justify-end pt-2">
                <AlertCircle
                  size={20}
                  className="text-amber-500 mt-0.5 flex-shrink-0 cursor-help"
                />
              </div>
            </TooltipTrigger>
            <TooltipContent className="bg-amber-950/90 dark:bg-amber-950/90 rounded-md border dark:border-amber-700/50 p-3">
              <div className="relative">
                <div className="flex items-start gap-3">
                  <div>
                    <p className="text-white font-medium">
                      For more accurate results, please upload at least 4
                      images. Maximum 10 images allowed.
                    </p>
                    <p className="text-white/80 text-sm mt-1">
                      Total image size cannot exceed 4.5MB.
                    </p>
                    <p className="text-blue-300 text-sm mt-1">
                      💡 Tip: You can select multiple images at once!
                    </p>
                  </div>
                </div>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {/* Stylized upload grid (3 in a row) */}
        <div className="grid grid-cols-3 gap-4 mb-6 mt-4">
          {images.map((_, index) => (
            <div
              key={index}
              className="relative aspect-square rounded-xl overflow-hidden"
            >
              <div
                onDragEnter={(e) => {
                  e.preventDefault();
                  setIsDragging(true);
                }}
                onDragLeave={(e) => {
                  e.preventDefault();
                  setIsDragging(false);
                }}
                onDragOver={(e) => e.preventDefault()}
                onDrop={(e) => handleDrop(e, index)}
                className={`w-full h-full ${
                  isDragging ? "ring-2 ring-purple-500" : ""
                }`}
              >
                <label className="w-full h-full block cursor-pointer">
                  <div
                    className="
                    w-full h-full
                    bg-black/40
                    hover:bg-black/50
                    transition-all
                    duration-300
                    flex
                    items-center
                    justify-center
                    relative
                  "
                  >
                    {isUploading &&
                    index === images.findIndex((img) => img === null) ? (
                      <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
                        <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      </div>
                    ) : null}
                    {images[index] ? (
                      <div className="relative w-full h-full group">
                        <img
                          src={imageUrls[index] || undefined}
                          alt={`Upload ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                        <div
                          className="
                          absolute
                          inset-0
                          bg-black/40
                          opacity-0
                          group-hover:opacity-100
                          flex
                          items-center
                          justify-center
                          transition-opacity
                        "
                        >
                          <button
                            type="button"
                            onClick={() => removeImage(index)}
                            className="
                            p-2
                            rounded-full
                            bg-red-500/80
                            hover:bg-red-600
                          "
                          >
                            <X size={20} className="text-white" />
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center space-y-2 text-white/60">
                        <Upload size={24} />
                        <span className="text-sm">Upload</span>
                        <span className="text-xs">Click or drop files</span>
                      </div>
                    )}
                  </div>
                  {/* Hidden file input */}
                  <input
                    type="file"
                    multiple={true}
                    accept="image/*"
                    onChange={(e) => handleImageChange(e, index)}
                    className="hidden"
                    disabled={isUploading}
                  />
                </label>
              </div>
            </div>
          ))}
        </div>

        {/* Add more images button - always show if we have less than 10 images */}
        {images.length < 10 && (
          <button
            type="button"
            onClick={addMoreImageSlot}
            className="
            w-full
            mb-6
            bg-purple-600/30
            hover:bg-purple-600/50
            text-white
            py-3
            px-6
            rounded-xl
            transition-all
            duration-300
            ease-in-out
            flex
            items-center
            justify-center
            gap-2
          "
          >
            <Plus size={18} />
            Add More Images
          </button>
        )}
      </>
    );
  }
);

export default ImageUploader;
