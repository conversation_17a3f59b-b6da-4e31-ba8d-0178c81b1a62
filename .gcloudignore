# # This file specifies files that are *not* uploaded to Google Cloud
# # using gcloud. It follows the same syntax as .gitignore, with the addition of
# # "#!include" directives (which insert the entries of the given .gitignore-style
# # file at that point).
# #
# # For more information, run:
# #   $ gcloud topic gcloudignore
# #
# .gcloudignore
# # If you would like to upload your .git directory, .gitignore file or files
# # from your .gitignore file, remove the corresponding line
# # below:
# .git
# .gitignore

# # Node.js dependencies:
# node_modules/node_modules/@next/swc-darwin-arm64/next-swc.darwin-arm64.node
# .next/cache/webpack/client-production/0.pack
# .next/cache/webpack/server-production/1.pack
# .next/cache/webpack/server-production/0.pack
# .git/objects/pack/pack-b525fd1cc44423cdb0b2d1fe1a824431c8118676.pack


.gcloudignore
.git
.gitignore
node_modules
