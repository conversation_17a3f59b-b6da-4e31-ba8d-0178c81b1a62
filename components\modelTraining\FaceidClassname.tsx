import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface FaceidClassnameProps {
  className: string;
  setClassName: React.Dispatch<React.SetStateAction<string>>;
}

const FaceidClassname: React.FC<FaceidClassnameProps> = ({
  className,
  setClassName,
}) => {
  // Define clothing options
  const clothingOptions = [
    { value: "clothing", label: "Clothing (General)" },
    { value: "shirt", label: "Shirt" },
    { value: "pants", label: "Pants" },
    { value: "coat", label: "Coat" },
    { value: "swimming suit", label: "Swimming Suit" },
  ];

  return (
    <div className="mt-4">
      <label className="block text-white/80 text-sm mb-2 font-medium">
        Class Name
      </label>
      <Select value={className} onValueChange={setClassName}>
        <SelectTrigger className="w-full bg-black/40 text-white placeholder-gray-400 rounded-lg px-4 py-7 focus:outline-none focus:ring-2 focus:ring-purple-500/70">
          <SelectValue placeholder="Select clothing type" />
        </SelectTrigger>
        <SelectContent className="bg-zinc-800 border-zinc-700">
          {clothingOptions.map((option) => (
            <SelectItem
              key={option.value}
              value={option.value}
              className="text-white hover:bg-zinc-700 focus:bg-zinc-700"
            >
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default FaceidClassname;
