"use client";
import { Post } from "@/types/blogs";
import { useState, useMemo } from "react";
import FeaturedPost from "./featured-post";
import CategoryTabs from "./category-tabs";
import SearchBar from "./search-bar";
import PostGrid from "./post-grid";
import Pagination from "./pagination";
import { useRouter, useSearchParams } from "next/navigation";

interface BlogSectionProps {
  posts: Post[];
}

export default function BlogSection({ posts }: BlogSectionProps) {
  // Read URL params once for initial state
  const searchParams = useSearchParams();
  const initialCategory = searchParams.get("category") || "All";
  const initialPage = parseInt(searchParams.get("page") || "1", 10);

  // Controlled states
  const [searchQuery, setSearchQuery] = useState("");
  const [activeCategory, setActiveCategory] = useState(initialCategory);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const router = useRouter();

  const postsPerPage = 6;

  // Memoize featured post
  const featuredPost = useMemo(
    () => posts.find((p) => p.featured) || posts[0],
    [posts]
  );

  // Memoize filtered posts
  const filteredPosts = useMemo(() => {
    let result = posts;
    if (activeCategory !== "All") {
      result = result.filter((p) => p.category === activeCategory);
    }
    if (searchQuery) {
      const q = searchQuery.toLowerCase();
      result = result.filter(
        (p) =>
          p.title.toLowerCase().includes(q) ||
          p.excerpt.toLowerCase().includes(q) ||
          p.category.toLowerCase().includes(q)
      );
    }
    return result;
  }, [posts, activeCategory, searchQuery]);

  // Calculate pagination
  const totalPages = Math.ceil(filteredPosts.length / postsPerPage);
  const currentPosts = useMemo(() => {
    const start = (currentPage - 1) * postsPerPage;
    return filteredPosts.slice(start, start + postsPerPage);
  }, [filteredPosts, currentPage]);

  // Sync page & category to URL on change
  const updateUrl = (category: string, page: number) => {
    const params = new URLSearchParams();
    if (category !== "All") params.set("category", category);
    if (page > 1) params.set("page", String(page));
    router.push(`?${params.toString()}`, { scroll: false });
  };

  const handleCategoryChange = (cat: string) => {
    setActiveCategory(cat);
    setCurrentPage(1);
    updateUrl(cat, 1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    updateUrl(activeCategory, page);
    document
      .getElementById("blog-content")
      ?.scrollIntoView({ behavior: "smooth", block: "center" });
  };

  return (
    <section
      className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12"
      aria-labelledby="blog-heading"
      id="blog-content"
    >
      {featuredPost && <FeaturedPost post={featuredPost} />}

      <div className="mt-12 space-y-6" id="category-tabs">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <CategoryTabs
            categories={["All", ...new Set(posts.map((p) => p.category))]}
            activeCategory={activeCategory}
            onCategoryChange={handleCategoryChange}
          />
          <SearchBar onSearch={setSearchQuery} />
        </div>

        <PostGrid posts={currentPosts} id="post-grid" />

        {filteredPosts.length > postsPerPage && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        )}

        {filteredPosts.length === 0 && (
          <div className="text-center py-12">
            <h2 className="text-xl font-medium text-gray-600 dark:text-gray-400">
              No posts found matching your criteria
            </h2>
            <button
              onClick={() => {
                setSearchQuery("");
                handleCategoryChange("All");
              }}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900 transition-colors"
            >
              Reset Filters
            </button>
          </div>
        )}
      </div>
    </section>
  );
}
