/**
 * Generate avatar using Astria's SDXL model with hardcoded model ID
 * @param user - The user ID (string)
 * @param prompt - The text prompt (string)
 * @param inputImage - FormData containing the input image for img2img (optional)
 * @param controlnet - ControlNet type to use (optional)
 * @param denoisingStrength - Denoising strength for img2img (optional)
 * @param style - Style to apply to the generated image (optional)
 * @returns { success: boolean, data?: string[], message?: string }
 */
export async function generateAvatar({
  user,
  prompt,
  inputImage,
}: {
  user: string;
  prompt: string;
  inputImage?: FormData | null;
}) {
  try {
    // Create FormData for the request
    const formData = new FormData();
    formData.append("user", user);
    formData.append("prompt", prompt);

    // Add image-to-image parameters if provided
    if (inputImage) {
      // Get the image file from the FormData
      const imageFile = inputImage.get("image") as File;
      formData.append("input_image", imageFile);
      formData.append("denoising_strength", "0.5");
    }

    // Call the avatar generation API
    const avatarRes = await fetch(`/api/proxy/avatar-generate`, {
      method: "POST",
      body: formData,
    });

    if (!avatarRes.ok) {
      const errorText = await avatarRes.text();
      return { success: false, message: errorText };
    }

    const avatarData = await avatarRes.json();

    // If we have an immediate result
    if (avatarData.imageUrl) {
      return {
        success: true,
        data: [avatarData.imageUrl],
      };
    }

    // If we need to poll for results
    const promptId = avatarData.id;

    // Poll for results
    while (true) {
      const resultRes = await fetch(`/api/proxy/prompt-result/${promptId}`);
      const resultData = await resultRes.json();
      if (resultData.images && resultData.images.length > 0) {
        return { success: true, data: resultData.images };
      }
      await new Promise((resolve) => setTimeout(resolve, 5000));
    }
  } catch (error: any) {
    return {
      success: false,
      message: error.message || "Unknown error occurred",
    };
  }
}
