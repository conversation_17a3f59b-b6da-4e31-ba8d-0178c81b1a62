import { render } from "@react-email/render";
import React from "react";

/**
 * Renders a React Email component to an HTML string.
 * @param Component The React Email component to render.
 * @param props The props to pass to the React Email component.
 * @returns A Promise that resolves to the HTML string representation of the email.
 */
export async function renderEmailHtml<P extends Record<string, unknown>>(
  Component: React.ComponentType<P>,
  props: P
): Promise<string> {
  return await render(React.createElement(Component, props));
}

