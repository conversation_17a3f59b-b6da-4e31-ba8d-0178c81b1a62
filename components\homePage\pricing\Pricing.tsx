"use client"
import { PricingPlans } from "./PricingPlans";
import { motion } from "framer-motion";

const Pricing = () => {
  return (
    <div className="min-h-[calc(100vh)] bg-gradient-to-b from-transparent to-gray-50/50 dark:to-gray-900/20">
      <div className="container mx-auto px-4 pt-20 pb-16">
        {/* Pricing Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.3,
            ease: "easeOut"
          }}
        >
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl mb-4">
            Simple Transparent Pricing
          </h1>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Choose the perfect plan for your needs. All plans include full access to our core features.
          </p>
        </motion.div>

        {/* Pricing Plans Grid */}
        <PricingPlans />
      </div>
    </div>
  );
};

export default Pricing;