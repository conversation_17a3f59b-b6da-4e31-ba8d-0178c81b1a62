"use client";

import React, { useState } from "react";
import { Download, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/Button";
import { useToast } from "@/hooks/use-toast";

interface DownloadButtonProps {
  images: string[];
  className?: string;
  variant?: "default" | "outline" | "secondary" | "ghost";
  size?: "default" | "sm" | "lg" | "icon";
}

const DownloadButton: React.FC<DownloadButtonProps> = ({
  images,
  className,
  variant = "outline",
  size = "sm",
}) => {
  const [isDownloading, setIsDownloading] = useState(false);
  const { toast } = useToast();

  const downloadSingleImage = async (imageUrl: string, filename: string) => {
    try {
      // Use the proxy API to avoid CORS issues
      const encodedUrl = encodeURIComponent(imageUrl);
      const proxyUrl = `/api/proxy/download/${encodedUrl}`;

      const response = await fetch(proxyUrl);
      if (!response.ok) throw new Error("Failed to fetch image");

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading image:", error);
      throw error;
    }
  };

  const downloadMultipleImages = async (imageUrls: string[]) => {
    try {
      // Dynamic import of JSZip to reduce bundle size
      const JSZip = (await import("jszip")).default;
      const zip = new JSZip();

      // Download all images and add to zip
      const downloadPromises = imageUrls.map(async (imageUrl, index) => {
        try {
          // Use the proxy API to avoid CORS issues
          const encodedUrl = encodeURIComponent(imageUrl);
          const proxyUrl = `/api/proxy/download/${encodedUrl}`;

          const response = await fetch(proxyUrl);
          if (!response.ok) throw new Error(`Failed to fetch image ${index + 1}`);

          const blob = await response.blob();
          const extension = getImageExtension(imageUrl) || "jpg";
          const filename = `burst-mode-image-${index + 1}.${extension}`;
          zip.file(filename, blob);
        } catch (error) {
          console.error(`Error downloading image ${index + 1}:`, error);
          // Continue with other images even if one fails
        }
      });

      await Promise.all(downloadPromises);

      // Generate and download zip file
      const zipBlob = await zip.generateAsync({ type: "blob" });
      const url = window.URL.createObjectURL(zipBlob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `burst-mode-images-${new Date().toISOString().split('T')[0]}.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error creating zip file:", error);
      throw error;
    }
  };

  const getImageExtension = (url: string): string => {
    try {
      const pathname = new URL(url).pathname;
      const extension = pathname.split('.').pop()?.toLowerCase();
      return ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension || '') 
        ? extension || 'jpg' 
        : 'jpg';
    } catch {
      return 'jpg';
    }
  };

  const handleDownload = async () => {
    if (images.length === 0) {
      toast({
        title: "No images to download",
        description: "Please generate some images first.",
        variant: "destructive",
      });
      return;
    }

    setIsDownloading(true);

    try {
      if (images.length === 1) {
        // Single image download
        const extension = getImageExtension(images[0]);
        const filename = `burst-mode-image.${extension}`;
        await downloadSingleImage(images[0], filename);
        
        toast({
          title: "Download complete",
          description: "Image downloaded successfully.",
        });
      } else {
        // Multiple images download as ZIP
        await downloadMultipleImages(images);
        
        toast({
          title: "Download complete",
          description: `${images.length} images downloaded as ZIP file.`,
        });
      }
    } catch (error) {
      console.error("Download failed:", error);
      toast({
        title: "Download failed",
        description: "There was an error downloading the images. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDownloading(false);
    }
  };

  if (images.length === 0) {
    return null;
  }

  return (
    <Button
      onClick={handleDownload}
      disabled={isDownloading}
      variant={variant}
      size={size}
      className={className}
    >
      {isDownloading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <Download className="h-4 w-4" />
      )}
      {size !== "icon" && (
        <span className="ml-2">
          {isDownloading 
            ? "Downloading..." 
            : images.length === 1 
              ? "Download" 
              : `Download (${images.length})`
          }
        </span>
      )}
    </Button>
  );
};

export default DownloadButton;
