import React from "react";
import { Metada<PERSON> } from 'next';
import <PERSON><PERSON><PERSON> from 'next/script';

export const metadata: Metadata = {
  title: "Privacy Policy | Burst Mode AI Photography",
  description: "Learn how Burst Mode collects, uses, and protects your personal information. Our privacy policy outlines our data practices, security measures, and your privacy rights.",
  keywords: [
    "privacy policy",
    "data protection",
    "personal information",
    "data security",
    "privacy rights",
    "Burst Mode privacy",
    "AI photography privacy",
    "user data",
    "cookies policy",
    "information collection"
  ],
  openGraph: {
    title: "Privacy Policy | Burst Mode AI Photography",
    description: "Learn how Burst Mode collects, uses, and protects your personal information. Our privacy policy outlines our data practices, security measures, and your privacy rights.",
    url: "https://burstmode.ai/privacy-policy",
    siteName: "Burst Mode AI",
    images: [
      {
        url: "https://burstmode.ai/app/og-icon.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Privacy Policy",
      }
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Privacy Policy | Burst Mode AI Photography",
    description: "Learn how <PERSON>urst Mode collects, uses, and protects your personal information.",
    images: [
      {
        url: "https://burstmode.ai/app/og-icon.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Privacy Policy",
      }
    ],
  },
  alternates: {
    canonical: "https://burstmode.ai/privacy-policy",
  },
};

const PrivacyPolicy: React.FC = () => {
  return (
    <>
      {/* Structured Data for SEO */}
      <Script
        id="privacy-policy-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "Privacy Policy | Burst Mode AI Photography",
            "description": "Learn how Burst Mode collects, uses, and protects your personal information. Our privacy policy outlines our data practices, security measures, and your privacy rights.",
            "url": "https://burstmode.ai/privacy-policy",
            "lastReviewed": "2025-05-01",
            "mainContentOfPage": {
              "@type": "WebPageElement",
              "cssSelector": ".container"
            },
            "speakable": {
              "@type": "SpeakableSpecification",
              "cssSelector": ["h1", "h2", "p"]
            },
            "publisher": {
              "@type": "Organization",
              "name": "Burst Mode AI",
              "url": "https://burstmode.ai",
              "logo": {
                "@type": "ImageObject",
                "url": "https://burstmode.ai/app/icon-transparent.png"
              }
            }
          })
        }}
      />
      <div className="container mx-auto px-6 py-12 max-w-3xl pt-20 text-gray-300">
        <h1 className="text-3xl font-bold mb-6">Burst Mode Privacy Policy</h1>
        <p className="text-gray-400 mb-4">Last Updated: 01/05/2025</p>

      <p className="mb-6">
        This Privacy Policy explains how Burst Mode collects, uses, and discloses information about you when you use our website and services ("Services").
      </p>

      {/* Section 1: Information We Collect */}
      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4">1. Information We Collect</h2>
        <p className="mb-2">We may collect the following types of information:</p>
        <ul className="list-disc list-inside space-y-2">
          <li>
            <strong>Information You Provide:</strong> When you use our Services, we may collect information you provide directly, such as:
            <ul className="list-disc list-inside ml-4">
              <li><strong>Account Information:</strong> Name, email address, and password when you create an account.</li>
              <li><strong>Payment Information:</strong> Credit card or other payment details when you make a purchase.</li>
              <li><strong>Contact Information:</strong> Phone number and address (if provided).</li>
              <li><strong>Photo Uploads:</strong> Images you upload to our platform for enhancement.</li>
            </ul>
          </li>
          <li>
            <strong>Information Automatically Collected:</strong> We may automatically collect certain information about your device and usage, such as:
            <ul className="list-disc list-inside ml-4">
              <li><strong>Device Information:</strong> Device type, operating system, browser type, and unique device identifiers.</li>
              <li><strong>Usage Information:</strong> Date and time of access, pages visited, features used, and interactions with our Services.</li>
              <li><strong>Location Information:</strong> Your general location (e.g., city, state) inferred from your IP address.</li>
            </ul>
          </li>
        </ul>
      </section>

      {/* Section 2: How We Use Your Information */}
      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4">2. How We Use Your Information</h2>
        <p>We use the collected information to:</p>
        <ul className="list-disc list-inside space-y-2">
          <li>Provide and improve our Services, including processing requests, enhancing photos, and providing customer support.</li>
          <li>Process payments and fulfill your orders.</li>
          <li>Communicate with you via updates, notifications, and marketing communications (opt-out available).</li>
          <li>Analyze usage patterns to improve our Services' functionality and performance.</li>
          <li>Protect our rights and interests, including preventing fraud and complying with legal obligations.</li>
        </ul>
      </section>

      {/* Section 3: Sharing Your Information */}
      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4">3. Sharing Your Information</h2>
        <p>We may share your information with:</p>
        <ul className="list-disc list-inside space-y-2">
          <li><strong>Service Providers:</strong> Third-party providers who assist with payments, cloud storage, and customer support.</li>
          <li><strong>Business Partners:</strong> In limited cases, with trusted partners for marketing or promotional purposes.</li>
          <li><strong>Legal Authorities:</strong> To comply with legal requests such as subpoenas or law enforcement investigations.</li>
        </ul>
      </section>

      {/* Section 4: Data Security */}
      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4">4. Data Security</h2>
        <p>
          We take reasonable measures to protect your information from unauthorized access, use, or disclosure. However, no method of internet transmission or electronic storage is completely secure.
        </p>
      </section>

      {/* Section 5: Cookies and Tracking Technologies */}
      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4">5. Cookies and Tracking Technologies</h2>
        <p>
          We may use cookies and similar tracking technologies to collect information about your usage of our Services. You can control cookies through your browser settings.
        </p>
      </section>

      {/* Section 6: Children's Privacy */}
      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4">6. Children's Privacy</h2>
        <p>
          Our Services are not intended for children under the age of 13. We do not knowingly collect personal information from children under 13.
        </p>
      </section>

      {/* Section 7: Changes to Privacy Policy */}
      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4">7. Changes to This Privacy Policy</h2>
        <p>
          We may update this Privacy Policy from time to time. We will notify you of any material changes by posting the updated policy on our website.
        </p>
      </section>



    </div>
    </>
  );
};

export default PrivacyPolicy;
