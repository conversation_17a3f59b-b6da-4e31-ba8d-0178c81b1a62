"use client";

import React from "react";
import { motion } from "framer-motion";
import { Loader2 } from "lucide-react";

const AuthLoadingScreen: React.FC = () => {
  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="flex flex-col items-center justify-center h-screen bg-gradient-to-b from-black/40 to-black/10"
    >
      <div className="bg-black/30 p-8 rounded-xl backdrop-blur-md border border-white/10 flex flex-col items-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-lg font-medium text-white/90">Verifying your account...</p>
      </div>
    </motion.div>
  );
};

export default AuthLoadingScreen;
