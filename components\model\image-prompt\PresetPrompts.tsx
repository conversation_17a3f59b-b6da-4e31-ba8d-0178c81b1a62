import React from "react";
import { PresetPromptsProps } from "./types";
import { PREDEFINED_PROMPTS } from "@/lib/constants";

export default function PresetPrompts({
  enhancementType,
  selectedPresetIndex,
  setSelectedPresetIndex,
  setPrompt,
  setEnhancedPrompt,
  setUseEnhancedPrompt
}: PresetPromptsProps) {
  // Get the appropriate prompts based on enhancement type
  const promptOptions = PREDEFINED_PROMPTS[enhancementType as keyof typeof PREDEFINED_PROMPTS] || [];

  // Handle preset prompt selection
  const selectPresetPrompt = (presetPrompt: string, index: number) => {
    setPrompt(presetPrompt);
    setSelectedPresetIndex(index);
    setEnhancedPrompt(null); // Clear any previous enhanced prompt
    setUseEnhancedPrompt(false);
  };

  return (
    <div>
      <label className="block text-white/90 text-sm mb-3 font-medium">
        Quick {enhancementType} prompts
      </label>
      <div className="flex flex-col space-y-2">
        {promptOptions.map((presetPrompt, index) => (
          <button
            key={index}
            onClick={() => selectPresetPrompt(presetPrompt, index)}
            className={`
              text-left
              px-4 py-3
              ${selectedPresetIndex === index
                ? 'bg-purple-500/20 border-purple-500/50'
                : 'bg-black/20 border-white/10 hover:bg-purple-500/10 hover:border-purple-500/30'}
              text-white/90
              rounded-lg
              transition-colors
              duration-200
              text-sm
              truncate
              border
            `}
          >
            {presetPrompt}
          </button>
        ))}
      </div>
    </div>
  );
}
