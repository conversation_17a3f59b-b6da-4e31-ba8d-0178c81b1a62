import { NextRequest, NextResponse } from "next/server";
import { adminDb } from "@/lib/firebase/adminConfig";

/**
 * Callback API for Astria tune completion
 * This endpoint is called by <PERSON>tri<PERSON> when a tune is finished training
 * It checks if a model with the same tuneId, userId, and modelType already exists.
 * If not, it saves the tune details to Firestore.
 */
export async function POST(request: NextRequest) {
  try {
    const tuneData = await request.json();

    const tune = tuneData.tune;
    if (!tune || !tune.id || !tune.created_at || !tune.title) { // Added more checks for required tune fields
      return NextResponse.json(
        { error: "Invalid or incomplete tune data structure" },
        { status: 400 }
      );
    }

    const { searchParams } = new URL(request.url);
    // Ensure required parameters are present and handle potential nulls safely
    const enhancementType = searchParams.get("enhancementType")?.replace(/"/g, '') || '';
    const modelType = searchParams.get("modelType")?.replace(/"/g, '');
    const userId = searchParams.get("userId")?.replace(/"/g, '');
    const tuneId = tune.id; // Use tune.id directly

    // Validate required parameters from query string
    if (!userId || !modelType) {
        return NextResponse.json(
            { error: "Missing required parameters: userId and modelType" },
            { status: 400 }
        );
    }

    // --- Check for existing document ---
    const modelsRef = adminDb.collection("models");
    const q = modelsRef
      .where("tuneId", "==", tuneId)
      .where("userId", "==", userId)
      .where("modelType", "==", modelType);

    const querySnapshot = await q.limit(1).get(); // Limit to 1 for efficiency

    if (!querySnapshot.empty) {
      // Document already exists
      const existingDocId = querySnapshot.docs[0].id;
      console.log(`Document already exists for tuneId: ${tuneId}, userId: ${userId}, modelType: ${modelType}. Doc ID: ${existingDocId}`);
      return NextResponse.json(
        {
          message: "Model entry already exists for this tune, user, and model type.",
          existingDocId: existingDocId,
          tuneId: tuneId,
        },
        { status: 200 } // Or 409 Conflict if you prefer to signal duplication as an error state
      );
    }
    // --- End check ---

    // Prepare document data if it doesn't exist
    const documentData = {
      createdAt: tune.created_at, // Consider using Firestore ServerTimestamp for consistency
      // createdAt: admin.firestore.FieldValue.serverTimestamp(), // Alternative
      enhancementType,
      title: tune.title,
      tuneId: tuneId, // Use the validated tuneId
      userId: userId, // Use the validated userId
      modelType: modelType // Use the validated modelType
    };

    // Save to Firestore
    const docRef = await adminDb.collection("models").add(documentData);

    console.log(`Successfully added new model document: ${docRef.id}`);
    return NextResponse.json({
      success: true,
      message: "Model entry created successfully.",
      savedDocId: docRef.id,
      data: documentData
    });

  } catch (error: any) {
    console.error("Error processing Astria callback:", error); // Log the actual error
    // Check if error is a Firestore error or other type if needed
    return NextResponse.json(
      { error: "Failed to process callback", details: error.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
