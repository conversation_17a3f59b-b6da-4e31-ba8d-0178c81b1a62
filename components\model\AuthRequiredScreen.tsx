"use client";

import React from "react";
import { motion } from "framer-motion";
import { LogIn } from "lucide-react";

interface AuthRequiredScreenProps {
  enhancementType: string;
}

const AuthRequiredScreen: React.FC<AuthRequiredScreenProps> = ({ enhancementType }) => {
  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex flex-col items-center justify-center h-screen bg-gradient-to-b from-black/40 to-black/10"
    >
      <motion.div 
        className="bg-black/30 p-8 rounded-xl backdrop-blur-md border border-white/10 flex flex-col items-center max-w-sm text-center"
        whileHover={{ scale: 1.02 }}
        transition={{ type: "spring", stiffness: 400, damping: 10 }}
      >
        <LogIn className="h-10 w-10 text-primary mb-4" />
        <h2 className="text-xl font-bold mb-2 text-white">Authentication Required</h2>
        <p className="text-white/70 mb-4">Please log in to access the {enhancementType} enhancement tools.</p>
        <motion.button 
          onClick={() => window.location.href = "/login"}
          className="px-6 py-3 bg-primary rounded-lg font-medium text-gray-800"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.98 }}
        >
          Sign In
        </motion.button>
      </motion.div>
    </motion.div>
  );
};

export default AuthRequiredScreen;
