import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ url: string }> }
) {
  try {
    const { url } = await params;
    const fileUrl = decodeURIComponent(url); // Decode back to original URL

    console.log("Downloading file from:", fileUrl);

    const response = await fetch(fileUrl);
    if (!response.ok) {
      return NextResponse.json({ error: "Failed to fetch file" }, { status: 400 });
    }

    const buffer = await response.arrayBuffer();
    const headers = new Headers();
    headers.set("Content-Type", response.headers.get("Content-Type") || "application/octet-stream");
    headers.set("Content-Disposition", 'attachment; filename="download.jpg"');

    return new NextResponse(Buffer.from(buffer), { headers });
  } catch (error) {
    console.error("Download error:", error);
    return NextResponse.json({ error: "Error downloading file" }, { status: 500 });
  }
}
