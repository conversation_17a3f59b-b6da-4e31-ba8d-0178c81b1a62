"use client";
import React from "react";
import {
  motion,
  useScroll,
  useTransform,
  useSpring,
  MotionValue,
} from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { ArrowRight, Check } from "lucide-react";
import { useUser } from "@/context/UserContext";
import { Button } from "@heroui/button";

export const HeroParallax = ({
  products,
}: {
  products: {
    title: string;
    link: string;
    thumbnail: string;
  }[];
}) => {

  const firstRow = products.slice(0, 10);
  const secondRow = products.slice(10, 20);
  const thirdRow = products.slice(20, 30);
  const fourthRow = products.slice(30, 40);

  const ref = React.useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start start", "end start"],
  });

  const springConfig = { stiffness: 350, damping: 40, bounce: 200 };

  const translateX = useSpring(
    useTransform(scrollYProgress, [0, 1], [0, 1000]),
    springConfig
  );
  const translateXReverse = useSpring(
    useTransform(scrollYProgress, [0, 1], [0, -1000]),
    springConfig
  );
  const rotateX = useSpring(
    useTransform(scrollYProgress, [0, 0.2], [15, 0]),
    springConfig
  );
  const opacity = useSpring(
    useTransform(scrollYProgress, [0, 0.2], [0.2, 1]),
    springConfig
  );
  const rotateZ = useSpring(
    useTransform(scrollYProgress, [0, 0.2], [20, 0]),
    springConfig
  );
  const translateY = useSpring(
    useTransform(scrollYProgress, [0, 0.45], [-1000, 500]),
    springConfig
  );

  return (

    <div
      ref={ref}
      className="h-[300vh] overflow-hidden antialiased relative flex flex-col self-auto [perspective:1000px] [transform-style:preserve-3d]"
    >
      
      <Header />
      <motion.div
        style={{
          rotateX,
          rotateZ,
          translateY,
          opacity,
          willChange: "transform, opacity",
        }}
      >
        {/* Row 1 */}
        <motion.div
          className="flex flex-row-reverse gap-8 mb-20"
          style={{
            willChange: "transform, opacity",
          }}
        >
          {firstRow.map((product, index) => (
            <ProductCard
              product={product}
              translate={translateX}
              // key={product.title}
              key={index}
            />
          ))}
        </motion.div>

        {/* Row 2 */}
        <motion.div
          className="flex flex-row gap-8 mb-20"
          style={{
            willChange: "transform, opacity",
          }}
        >
          {secondRow.map((product, index) => (
            <ProductCard
              product={product}
              translate={translateXReverse}
              // key={product.title}
              key={index}
            />
          ))}
        </motion.div>

        {/* Row 3 */}
        <motion.div
          className="flex flex-row-reverse gap-8 mb-20"
          style={{
            willChange: "transform, opacity",
          }}
        >
          {thirdRow.map((product, index) => (
            <ProductCard
              product={product}
              translate={translateX}
              // key={product.title}
              key={index}
            />
          ))}
        </motion.div>

        {/* Row 4 */}
        <motion.div
          className="flex flex-row gap-8 mb-20"
          style={{
            willChange: "transform, opacity",
          }}
        >
          {fourthRow.map((product, index) => (
            <ProductCard
              product={product}
              translate={translateXReverse}
              // key={product.title}
              key={index}
            />
          ))}
        </motion.div>
      </motion.div>
    </div>
  );
};

export const Header = () => {
  const {user} = useUser();
  return (
    <div className="w-full max-w-7xl mx-auto px-4 lg:px-8 lg:py-16 z-50 relative  min-h-screen flex items-center">
      <div className="flex flex-col lg:flex-row  gap-12 w-full">
        {/* Left content */}
        <div className="lg:w-1/2 flex flex-col gap-8 pt-8">
          <div className="space-y-4">
            <div className="inline-flex items-center gap-2 bg-gray-800/50 px-4 py-2 rounded-full">
              <span className="px-2 py-1 bg-gray-600 text-white text-xs rounded-full">
                New
              </span>
              <span className="text-sm font-medium">
                Effortless AI Photo Enhancements
              </span>
            </div>

            <div className="space-y-8 p-4 w-full mt-5 bg-gray-800/50 rounded-lg">
              <h1 className="text-4xl lg:text-6xl font-bold  tracking-tight">
                Transform Your Photos with{" "}
                <span className="text-gradient bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600">
                  AI!
                </span>
              </h1>
              <p className="text-lg leading-relaxed">
                Effortlessly generate enhanced headshots, product images, and
                food photos with our cutting-edge AI technology.
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
              <Link href={user ? "/model":"/register"}>
                  <Button
                    variant="ghost"
                    size="lg"
                    className="w-full sm:w-auto text-lg font-semibold group"
                  >
                    
                    {user ? <>Start Generating Now</> : <>Create An Account</>}
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition" />
                  </Button>
                </Link>
                <Link href="/gallery">
                  <Button
                    variant="ghost"
                    size="lg"
                    className="w-full sm:w-auto text-lg font-semibold"
                  >
                    View Gallery
                  </Button>
                </Link>
              </div>

              {/* Features list */}
              <div className="grid grid-cols-2 gap-4 mt-5">
                {[
                  "Effortless Enhancement through AI",
                  "Stunning Results",
                  "Fast and Efficient",
                  "User-Friendly Interface",
                ].map((feature) => (
                  <div key={feature} className="flex items-center gap-2">
                    <div className="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center">
                      <Check className="h-4 w-4 text-primary" />
                    </div>
                    <span className="text-sm font-medium">{feature}</span>
                  </div>
                ))}
              </div>

              {/* Social proof */}
              <div className="flex flex-col sm:flex-row items-center gap-6 mt-5 p-4 bg-gray-800/50 rounded-lg">
                <div className="flex -space-x-3">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div
                      key={i}
                      className="h-10 w-10 rounded-full border-2 border-white overflow-hidden"
                    >
                      <Image
                        src={`/_avatars/${i}.jpg`}
                        alt="User avatar"
                        width={40}
                        height={40}
                        className="h-full w-full object-cover"
                      />
                    </div>
                  ))}
                </div>
                <div className="flex flex-col">
                  <p className="font-semibold">123,456,789+</p>
                  <p className="text-sm text-muted-foreground">
                    AI generated images
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right content - Preview Image */}
        <div className="lg:w-1/2 relative mt-20">
          <div className="relative aspect-square max-w-[500px] mx-auto">
            <div className="absolute inset-0 bg-gradient-to-tr from-primary to-green-500 rounded-3xl blur-3xl opacity-50" />
            <div className="relative bg-gray-800/50 rounded-3xl p-2 border border-primary/10">
              <div className="aspect-square rounded-2xl bg-secondary overflow-hidden">
                <Image
                  src="/headshot.jpg"
                  alt="AI Generation Preview"
                  width={500}
                  height={500}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export const ProductCard = ({
  product,
  translate,
}: {
  product: {
    title: string;
    link: string;
    thumbnail: string;
  };
  translate: MotionValue<number>;
}) => {
  return (
    <motion.div
      style={{
        x: translate,
        willChange: "transform, opacity",
      }}
      whileHover={{
        y: -20,
      }}
      className="group relative flex-shrink-0 w-60 h-60"
    >
      <Link href={product.link} className="block w-full h-full">
        <div className="relative w-full h-full overflow-hidden rounded-lg border border-white">
          <Image
            src={product.thumbnail}
            alt={product.title}
            fill
            style={{ objectFit: "cover" }}
            className="transition-transform duration-300 ease-in-out group-hover:scale-105"
          />
        </div>
      </Link>
      <div className="absolute inset-0 h-full w-full opacity-0 group-hover:opacity-80 bg-black pointer-events-none"></div>
      <h2 className="absolute bottom-4 left-4 opacity-0 group-hover:opacity-100 text-white">
        {product.title}
      </h2>
    </motion.div>
  );
};
