import { Button } from '@/components/ui/Button'
import React from 'react'

const GapCard = () => {
  return (
    <div className="flex items-center flex-col justify-center container mx-auto px-4 py-8 lg:px-8 lg:py-16">
      <div className="rounded-xl border-4 border-primary grid grid-cols-3 w-full">
        <div className='col-span-2 flex flex-col items-center p-8 gap-4 bg-primary/10 justify-center'>
          <h1 className='font-bold lg:text-3xl text-2xl'>Start taking AI photos now</h1>
          <p className='max-w-screen-md'>Generate photorealistic images of people with AI. Take stunning photos of people with the first AI Photographer! Generate photo and video content for your social media with AI. Save time and money and do an AI photo shoot from your laptop or phone instead of hiring an expensive photographer</p>
          <Button variant="destructive" size="lg" className="font-bold text-lg mt-8">Create your AI model</Button>
        </div>
        <div>
          <img src="/headshot.jpg" alt="" className='h-full'/>
        </div>
      </div>
    </div>
  );
};

export default GapCard
