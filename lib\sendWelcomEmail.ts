"use server"
import { welcomeEmailTemplate } from "@/emailTemplates/welcomeEmailTemplate";
import { Resend } from "resend";

const resend = new Resend(process.env.RESEND_API_KEY as string);

export async function sendWelcomeEmail(to: string) {
  try {
    const emailHtml = await welcomeEmailTemplate();

    const { data, error } = await resend.emails.send({
      from: "BurstMode.ai <<EMAIL>>",
      to: [to],
      subject: "Welcome",
      html: emailHtml,
    });

    if (error) {
      console.error("Error sending email:", error.message);
      throw new Error(error.message);
    }

    if (data) {
      console.log("Welcome email sent successfully:", data);
    }
  } catch (error: any) {
    console.error("Error in sendWelcomeEmail function:", error);
  }
}
