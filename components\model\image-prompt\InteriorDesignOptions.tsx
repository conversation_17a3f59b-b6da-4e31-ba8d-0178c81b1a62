"use client";

import React from "react";
import { Home, Layers, Wand2 } from "lucide-react";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import type { InteriorDesignOptions } from "@/types/interiorDesign";

interface InteriorDesignOptionsProps {
  options: InteriorDesignOptions;
  setOptions: React.Dispatch<React.SetStateAction<InteriorDesignOptions>>;
}

export default function InteriorDesignOptions({
  options,
  setOptions,
}: InteriorDesignOptionsProps) {

  const handleMaskingToggle = (enabled: boolean) => {
    setOptions((prev) => ({
      ...prev,
      useMasking: enabled,
    }));
  };

  const handleMaskInvertToggle = (enabled: boolean) => {
    setOptions((prev) => ({
      ...prev,
      maskInvert: enabled,
    }));
  };

  const handleMaskPromptChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setOptions((prev) => ({
      ...prev,
      maskPrompt: e.target.value,
    }));
  };

  return (
    <div className="space-y-4 border border-white/10 rounded-lg p-4 bg-black/20">
      <div className="flex items-center gap-2 mb-2">
        <Home size={16} className="text-purple-400" />
        <h3 className="text-white/90 text-sm font-medium">
          Interior Design Options
        </h3>
      </div>

      {/* Masking Options */}
      <div className="pt-2 border-t border-white/10 space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Wand2 size={14} className="text-purple-400" />
            <Label htmlFor="use-masking" className="text-xs text-white/80">
              Mask Windows & Doors
            </Label>
          </div>
          <Switch
            id="use-masking"
            checked={options.useMasking}
            onCheckedChange={handleMaskingToggle}
          />
        </div>

        {options.useMasking && (
          <div className="pl-6 space-y-3">
            <div className="space-y-2">
              <Label
                htmlFor="mask-prompt"
                className="text-xs text-white/60 block"
              >
                Mask Prompt
              </Label>
              <input
                id="mask-prompt"
                type="text"
                value={options.maskPrompt}
                onChange={handleMaskPromptChange}
                placeholder="windows door"
                className="w-full bg-black/30 border border-white/10 rounded-md p-2 text-sm text-white"
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="mask-invert" className="text-xs text-white/60">
                Invert Mask
              </Label>
              <Switch
                id="mask-invert"
                checked={options.maskInvert}
                onCheckedChange={handleMaskInvertToggle}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
