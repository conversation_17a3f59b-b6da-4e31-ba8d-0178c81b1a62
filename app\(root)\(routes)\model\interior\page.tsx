import React from "react";
import EnhancementWrapper from "@/components/model/EnhancementWrapper";
import { Metadata } from 'next';
import Script from 'next/script';

export const metadata: Metadata = {
  title: "AI Interior Design Visualization | Burst Mode",
  description: "Transform your interior spaces with Burst Mode's AI technology. Create stunning room designs, visualize renovations, and explore different styles for homes, offices, and commercial spaces.",
  keywords: [
    "AI interior design",
    "room visualization",
    "interior design AI",
    "home renovation visualization",
    "AI room design",
    "interior space planning",
    "virtual interior design",
    "AI home design",
    "interior visualization tool",
    "room makeover AI"
  ],
  openGraph: {
    title: "AI Interior Design Visualization | Burst Mode",
    description: "Transform your interior spaces with Burst Mode's AI technology. Create stunning room designs, visualize renovations, and explore different styles for homes, offices, and commercial spaces.",
    url: "https://burstmode.ai/model/interior",
    siteName: "Burst Mode AI",
    images: [
      {
        url: "https://burstmode.ai/app/og-icon.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Interior Design",
      }
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "AI Interior Design Visualization | Burst Mode",
    description: "Transform your interior spaces with Burst Mode's AI technology. Create stunning room designs and visualize renovations.",
    images: [
      {
        url: "https://burstmode.ai/app/og-icon.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Interior Design",
      }
    ],
  },
  alternates: {
    canonical: "https://burstmode.ai/model/interior",
  },
};

const InteriorDesignPage = () => {
  return (
    <>
      {/* Structured Data for SEO */}
      <Script
        id="interior-design-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "AI Interior Design Visualization",
            "description": "Transform your interior spaces with Burst Mode's AI technology. Create stunning room designs, visualize renovations, and explore different styles for homes, offices, and commercial spaces.",
            "url": "https://burstmode.ai/model/interior",
            "mainEntity": {
              "@type": "Service",
              "name": "AI Interior Design Visualization",
              "provider": {
                "@type": "Organization",
                "name": "Burst Mode AI",
                "url": "https://burstmode.ai"
              },
              "description": "Create and visualize interior designs for any space using advanced AI technology",
              "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "USD"
              }
            }
          })
        }}
      />
      <EnhancementWrapper enhancementType="interior" />
    </>
  );
};

export default InteriorDesignPage;
