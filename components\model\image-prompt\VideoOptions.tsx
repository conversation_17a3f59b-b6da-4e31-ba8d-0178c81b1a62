import React from "react";
import { Video } from "lucide-react";

interface VideoOptionsProps {
  videoPrompt: string;
  setVideoPrompt: React.Dispatch<React.SetStateAction<string>>;
  videoModel: '720p' | '480p';
  setVideoModel: React.Dispatch<React.SetStateAction<'720p' | '480p'>>;
}

export default function VideoOptions({
  videoPrompt,
  setVideoPrompt,
  videoModel,
  setVideoModel,
}: VideoOptionsProps) {
  return (
    <div className="space-y-4 p-4 bg-black/20 rounded-lg border border-white/10">
      <div className="flex items-center gap-2 mb-2">
        <Video className="h-4 w-4 text-purple-400" />
        <h3 className="text-white/90 text-sm font-medium">Video Generation Options</h3>
      </div>
      
      <div className="space-y-2">
        <label className="block text-white/70 text-xs font-medium">
          Video Prompt (optional)
        </label>
        <textarea
          value={videoPrompt}
          onChange={(e) => setVideoPrompt(e.target.value)}
          placeholder="Describe camera movement, scene changes, or object interactions for the video"
          className="
            w-full
            bg-black/30
            border border-white/10
            rounded-lg
            p-3
            text-white
            placeholder-white/30
            focus:outline-none
            focus:ring-2
            focus:ring-purple-500/50
            resize-none
            h-24
          "
        />
        <p className="text-white/50 text-xs">
          If not provided, the image prompt will be used for the video.
        </p>
      </div>
      
      <div className="space-y-2">
        <label className="block text-white/70 text-xs font-medium">
          Video Resolution
        </label>
        <div className="flex gap-3">
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="radio"
              name="videoModel"
              value="720p"
              checked={videoModel === '720p'}
              onChange={() => setVideoModel('720p')}
              className="text-purple-600 focus:ring-purple-500"
            />
            <span className="text-white/80 text-sm">720p (HD)</span>
          </label>
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="radio"
              name="videoModel"
              value="480p"
              checked={videoModel === '480p'}
              onChange={() => setVideoModel('480p')}
              className="text-purple-600 focus:ring-purple-500"
            />
            <span className="text-white/80 text-sm">480p (SD)</span>
          </label>
        </div>
      </div>
    </div>
  );
}
