"use client";
import React from "react";
import Link from "next/link";
import { LogOut, Settings } from "lucide-react";
import { getInitials } from "@/lib/utils";
import { useUser } from "@/context/UserContext";
import { Button } from "./ui/Button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const UserDropdown = () => {
  const { user, logout } = useUser();

  
  if (!user) {
    return (
      <Link href="/login">
        <Button variant="outline" size="sm">
          Log In
        </Button>
      </Link>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="relative inline-flex items-center justify-center w-10 h-10 overflow-hidden bg-purple-400 rounded-full dark:bg-purple-700 cursor-pointer hover:opacity-80">
          <span className="font-medium mt-1 text-gray-600 dark:text-gray-300">
            {getInitials(user.fullname)}
          </span>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end">
        <DropdownMenuLabel>
          <div className="flex flex-col">
            <span className="font-semibold">{user.fullname}</span>
            <span className="text-xs text-gray-500 truncate">
              {user.email}
            </span>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem className="hover:cursor-pointer">
          <Link href="/account-settings" className="flex items-center w-full">
            <Settings className="mr-2 h-4 w-4" />
            <span>Account Settings</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem className="hover:cursor-pointer" onClick={logout}>
          <LogOut className="mr-2 h-4 w-4" />
          <span>Log out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default UserDropdown;
