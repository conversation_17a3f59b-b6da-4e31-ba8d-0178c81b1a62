"use client";

import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useUser } from "@/context/UserContext";
import { confirmSubscription } from "@/lib/stripe";

const PaymentSuccessContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useUser();
  const priceId = searchParams.get("priceId");

  useEffect(() => {
    if (user && priceId) {
      confirmSubscription(user.uid, priceId)
        .then((res) => {
          router.push("/account-settings"); 
        })
        .catch((error) => {
          console.error("Error confirming subscription:", error);
        });
    }
  }, [user, priceId, router]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center">
      <h1 className="text-2xl font-bold">Payment Successful!</h1>
      <p>Your subscription is being activated...</p>
    </div>
  );
};

export default PaymentSuccessContent; 