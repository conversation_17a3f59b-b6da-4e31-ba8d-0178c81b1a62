// Interior Design specific types

export interface InteriorDesignOptions {
  // ControlNet options
  useSegroom: boolean;
  useMlsd: boolean;
  controlnetWeights: {
    segroom: number;
    mlsd: number;
  };
  
  // Masking options
  useMasking: boolean;
  maskPrompt: string;
  maskInvert: boolean;
}

export interface InteriorDesignPrompt {
  text: string;
  description: string;
  style: string;
  category: 'scandinavian' | 'modern' | 'minimalist' | 'industrial' | 'traditional' | 'other';
}
