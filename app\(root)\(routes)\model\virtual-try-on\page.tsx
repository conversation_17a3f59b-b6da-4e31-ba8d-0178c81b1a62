import VirtualTryOnWrapper from "@/components/model/VirtualTryOnWrapper";
import { Metadata } from "next";
import <PERSON>rip<PERSON> from "next/script";

export const metadata: Metadata = {
  title: "AI Image Enhancement Tools & Features | Burst Mode",
  description:
    "Explore Burst Mode's suite of AI image enhancement tools. Transform headshots, food photography, product images, try on virtual clothing, design interiors, upscale images, and convert images to videos.",
  keywords: [
    "AI image enhancement",
    "professional headshots",
    "food photography",
    "product photography",
    "virtual try-on",
    "interior design visualization",
    "image upscaling",
    "image to video conversion",
    "AI photography tools",
    "photo enhancement",
  ],
  openGraph: {
    title: "AI Image Enhancement Tools & Features | Burst Mode",
    description:
      "Explore Burst Mode's suite of AI image enhancement tools. Transform headshots, food photography, product images, try on virtual clothing, design interiors, upscale images, and convert images to videos.",
    url: "https://burstmode.ai/model",
    siteName: "Burst Mode AI",
    images: [
      {
        url: "https://burstmode.ai/og-virtual-try-on.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Image Enhancement Tools",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "AI Image Enhancement Tools & Features | Burst Mode",
    description:
      "Explore Burst Mode's suite of AI image enhancement tools for photos and videos.",
    images: [
      {
        url: "https://burstmode.ai/og-virtual-try-on.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Image Enhancement Tools",
      },
    ],
  },
  alternates: {
    canonical: "https://burstmode.ai/model",
  },
};

export default function page() {
  return (
    <>
      <Script
        id="model-page-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "CollectionPage",
            name: "AI Image Enhancement Tools & Features",
            description:
              "Explore Burst Mode's suite of AI image enhancement tools. Transform headshots, food photography, product images, try on virtual clothing, design interiors, upscale images, and convert images to videos.",
            url: "https://burstmode.ai/model",
            publisher: {
              "@type": "Organization",
              name: "Burst Mode AI",
              url: "https://burstmode.ai",
              logo: {
                "@type": "ImageObject",
                url: "https://burstmode.ai/app/icon.png",
              },
            },
            mainEntity: {
              "@type": "ItemList",
              itemListElement: [
                {
                  "@type": "ListItem",
                  position: 1,
                  url: "https://burstmode.ai/model/headshots",
                  name: "Professional Headshots",
                },
                {
                  "@type": "ListItem",
                  position: 2,
                  url: "https://burstmode.ai/model/foods",
                  name: "Food Photography Enhancement",
                },
                {
                  "@type": "ListItem",
                  position: 3,
                  url: "https://burstmode.ai/model/products",
                  name: "Product Photography Enhancement",
                },
                {
                  "@type": "ListItem",
                  position: 4,
                  url: "https://burstmode.ai/model/virtual-try-on",
                  name: "Virtual Try-On",
                },
                {
                  "@type": "ListItem",
                  position: 5,
                  url: "https://burstmode.ai/model/interior",
                  name: "Interior Design",
                },
                {
                  "@type": "ListItem",
                  position: 6,
                  url: "https://burstmode.ai/model/upscaling",
                  name: "Image Upscaling",
                },
                {
                  "@type": "ListItem",
                  position: 7,
                  url: "https://burstmode.ai/model/image-to-video",
                  name: "Image to Video Conversion",
                },
              ],
            },
          }),
        }}
      />
      <VirtualTryOnWrapper enhancementType="virtual try-on" />
    </>
  );
}
