import React from "react";
import EmailLayout from "./EmailLayout";

type FeatureSuggestionEmailProps = {
  data: {
    name: string;
    email: string;
    category: string;
    priority: string;
    description: string;
    submittedAt: string;
  };
};

export const FeatureSuggestionEmail: React.FC<FeatureSuggestionEmailProps> = ({
  data: { name, email, category, priority, description, submittedAt },
}) => {
  return (
    <EmailLayout>
      <div className="bg-gray-50 text-gray-800">
        <div className="max-w-2xl mx-auto p-6 bg-white shadow-md rounded-lg mt-10">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-blue-600 mb-2">
              📥 New Feature Suggestion Received
            </h2>
            <p className="text-gray-600 text-sm">
              A user has submitted a new feature suggestion through the app.
              Details are below.
            </p>
          </div>

          <div className="mt-6 border-t pt-6">
            <h3 className="text-lg font-semibold text-gray-800">
              Submission Details:
            </h3>
            <div className="mt-4 space-y-2 text-sm text-gray-700">
              <p>
                <strong>Name:</strong> {name}
              </p>
              <p>
                <strong>Email:</strong> {email}
              </p>
              <p>
                <strong>Category:</strong> {category}
              </p>
              <p>
                <strong>Priority:</strong> {priority}
              </p>
              <p>
                <strong>Submitted At:</strong> {submittedAt}
              </p>
              <p>
                <strong>Description:</strong>
              </p>
              <p className="bg-gray-100 p-3 rounded text-sm text-gray-800 whitespace-pre-line">
                {description}
              </p>
            </div>
          </div>

          <div className="mt-6">
            <p className="text-sm text-gray-600">
              Please review this suggestion and determine whether it aligns with
              the product roadmap.
            </p>
            <p className="mt-4 text-sm text-gray-500">
              — Automated Notification
            </p>
          </div>

          <div className="mt-8 text-center text-xs text-gray-400">
            © {2025} Burst Mode Admin System
          </div>
        </div>
      </div>
    </EmailLayout>
  );
};

