const ASTRIA_API_BASE_URL = "https://api.astria.ai";
import axios from "axios";
import { AstriaPromptResponse } from "@/types/astria";

/**
 * Service class for interacting with Astria AI Prompt API
 */
export class AstriaPromptService {
  private apiKey: string;

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.NEXT_PUBLIC_ASTRIA_API_KEY || "";

    if (!this.apiKey) {
      throw new Error("Astria API key is required");
    }
  }

  /**
   * Get a prompt by tune ID and prompt ID
   * @param tuneId The tune ID
   * @param promptId The prompt ID
   * @returns Promise with prompt data
   */
  async getPromptBytuneIdAndPromptId(
    tuneId: string,
    promptId: string
  ): Promise<AstriaPromptResponse> {
    try {
      const url = `${ASTRIA_API_BASE_URL}/tunes/${tuneId}/prompts/${promptId}`;
      const response = await axios.get<any>(url, {
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          "Content-Type": "application/json",
        },
      });

      return {
        data: response.data,
        success: true,
        message: "Successfully fetched prompt",
      };
    } catch (error: any) {
      throw new Error(error.message);
    }
  }

  /**
   * Get a prompt by prompt ID only (for pack prompts)
   * @param promptId The prompt ID
   * @returns Promise with prompt data
   */
  async getPromptById(promptId: string): Promise<AstriaPromptResponse> {
    try {
      const url = `${ASTRIA_API_BASE_URL}/prompts/${promptId}.json`;
      const response = await axios.get<any>(url, {
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          "Content-Type": "application/json",
        },
      });

      return {
        data: response.data,
        success: true,
        message: "Successfully fetched prompt",
      };
    } catch (error: any) {
      throw new Error(error.message);
    }
  }
}
