import React from "react";
import { ImagePromptContainer } from "./image-prompt";

interface ImagePromptProps {
  modelId: string;
  userId: string;
  title: string;
  imageUrl: string | null;
  setImageUrl: React.Dispatch<React.SetStateAction<string | null>>;
  onStartProcessing: () => void;
  onCompleteProcessing: () => void;
  enhancementType: string;
  garmentModelId?: string;
}

export default function ImagePrompt(props: ImagePromptProps) {
  return <ImagePromptContainer {...props} />;
}
