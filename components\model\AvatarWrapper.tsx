"use client";

import React, { useState } from "react";
import { useAuthState } from "react-firebase-hooks/auth";
import { auth } from "@/lib/firebase";
import AvatarPrompt from "@/components/model/avatar-prompt/AvatarPrompt";
import { usePremiumStatus } from "@/lib/stripe";
import { motion, AnimatePresence } from "framer-motion";
import BurstModeLoadingScreen from "../ui/GenerationLoadingScreen";

// Imported extracted components
import AuthLoadingScreen from "./AuthLoadingScreen";
import AuthRequiredScreen from "./AuthRequiredScreen";
import PremiumRequiredScreen from "./PremiumRequiredScreen";
import EnhancementHeader from "./EnhancementHeader";
import ScreenContentContainer from "./ScreenContentContainer";
import ScreenTransition from "./ScreenTransition";

type ScreenState = "prompt" | "processing";

const AvatarWrapper: React.FC = () => {
  const [user, loadingUser] = useAuthState(auth);
  const [screen, setScreen] = useState<ScreenState>("prompt");
  const [imageUrl, setImageUrl] = useState<string | null>(null);

  const premiumStatus = usePremiumStatus(user);
  // const premiumStatus = true;

  if (loadingUser) {
    return <AuthLoadingScreen />;
  }

  if (!user) {
    return <AuthRequiredScreen enhancementType="avatar" />;
  }

  if (!premiumStatus) {
    return <PremiumRequiredScreen enhancementType="avatar" />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-black/20 to-black/5">
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-4xl mx-auto px-2 sm:px-4 pt-16 md:pt-28 pb-16 w-full"
      >
        <EnhancementHeader
          enhancementType="avatar"
          currentScreen={screen}
        />

        <ScreenContentContainer>
          <AnimatePresence mode="wait">
            {screen === "prompt" && (
              <ScreenTransition screenKey="prompt" direction="right">
                <AvatarPrompt
                  userId={user.uid}
                  imageUrl={imageUrl}
                  setImageUrl={setImageUrl}
                  onStartProcessing={() => setScreen("processing")}
                  onCompleteProcessing={() => setScreen("prompt")}
                />
              </ScreenTransition>
            )}

            {screen === "processing" && <BurstModeLoadingScreen />}
          </AnimatePresence>
        </ScreenContentContainer>
      </motion.div>
    </div>
  );
};

export default AvatarWrapper;
