import { db } from "../config";
import {
  collection,
  query,
  where,
  doc,
  getDocs,
  addDoc,
  serverTimestamp,
  updateDoc,
  increment,
  getDoc,
  setDoc,
  deleteDoc,
  DocumentData,
  QuerySnapshot
} from "firebase/firestore";

/**
 * Get user data from Firestore
 * @param userId - The user ID
 * @returns User data or null if not found
 */
export const getUserData = async (userId: string) => {
  try {
    const userDoc = await getDoc(doc(db, "users", userId));
    if (userDoc.exists()) {
      return { success: true, data: userDoc.data() };
    } else {
      return { success: false, message: "User not found" };
    }
  } catch (error: any) {
    return { success: false, message: error.message || "Failed to get user data" };
  }
};

/**
 * Get user's subscribed plan
 * @param userId - The user ID
 * @returns Plan data or null if not found
 */
export const getUserPlan = async (userId: string) => {
  try {
    const subscribedPlanRef = collection(db, "users", userId, "subscribedPlan");
    const querySnapshot = await getDocs(subscribedPlanRef);

    if (querySnapshot.empty) {
      return { success: false, message: "No subscribed plan found" };
    }

    // Get the first document in the subscribedPlan collection
    const planDoc = querySnapshot.docs[0];
    return { 
      success: true, 
      data: {
        planId: planDoc.id,
        ...planDoc.data()
      }
    };
  } catch (error: any) {
    return { success: false, message: error.message || "Failed to get user plan" };
  }
};

/**
 * Update user's image credits
 * @param userId - The user ID
 * @param amount - Amount to decrement (negative) or increment (positive)
 */
export const updateImageCredits = async (userId: string, amount: number) => {
  try {
    const subscribedPlanRef = collection(db, "users", userId, "subscribedPlan");
    const querySnapshot = await getDocs(subscribedPlanRef);

    if (querySnapshot.empty) {
      return { success: false, message: "No subscribed plan found" };
    }

    // Get the first document in the subscribedPlan collection
    const planDoc = querySnapshot.docs[0];
    const planDocId = planDoc.id;
    const planRef = doc(db, "users", userId, "subscribedPlan", planDocId);
    
    await updateDoc(planRef, {
      planImageCredits: increment(amount)
    });

    return { success: true };
  } catch (error: any) {
    return { success: false, message: error.message || "Failed to update image credits" };
  }
};

/**
 * Update user's model credits
 * @param userId - The user ID
 * @param amount - Amount to decrement (negative) or increment (positive)
 */
export const updateModelCredits = async (userId: string, amount: number) => {
  try {
    const subscribedPlanRef = collection(db, "users", userId, "subscribedPlan");
    const querySnapshot = await getDocs(subscribedPlanRef);

    if (querySnapshot.empty) {
      return { success: false, message: "No subscribed plan found" };
    }

    // Get the first document in the subscribedPlan collection
    const planDoc = querySnapshot.docs[0];
    const planDocId = planDoc.id;
    const planRef = doc(db, "users", userId, "subscribedPlan", planDocId);
    
    await updateDoc(planRef, {
      planCredits: increment(amount)
    });

    return { success: true };
  } catch (error: any) {
    return { success: false, message: error.message || "Failed to update model credits" };
  }
};

/**
 * Save a new model to Firestore
 * @param userId - The user ID
 * @param tuneId - The Astria tune ID
 * @param title - The model title
 * @param enhancementType - The enhancement type (headshot, product, food)
 */
export const saveModel = async (
  userId: string,
  tuneId: string,
  title: string,
  enhancementType: string
) => {
  try {
    const modelRef = await addDoc(collection(db, "models"), {
      userId,
      title,
      tuneId,
      enhancementType,
      createdAt: serverTimestamp(),
    });

    return { success: true, modelId: modelRef.id };
  } catch (error: any) {
    return { success: false, message: error.message || "Failed to save model" };
  }
};

/**
 * Get user's models
 * @param userId - The user ID
 * @param enhancementType - Optional filter by enhancement type
 */
export const getUserModels = async (userId: string, enhancementType?: string) => {
  try {
    let q;
    if (enhancementType) {
      q = query(
        collection(db, "models"),
        where("userId", "==", userId),
        where("enhancementType", "==", enhancementType)
      );
    } else {
      q = query(
        collection(db, "models"),
        where("userId", "==", userId)
      );
    }

    const snapshot = await getDocs(q);
    
    const models: any[] = [];
    snapshot.forEach((doc) => {
      models.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return { success: true, models };
  } catch (error: any) {
    return { success: false, message: error.message || "Failed to get user models" };
  }
};
