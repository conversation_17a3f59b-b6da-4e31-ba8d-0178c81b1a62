import { NextRequest, NextResponse } from "next/server";
import { GoogleGenAI } from "@google/genai";

// Initialize Google Generative AI with your API key
const genAI = new GoogleGenAI({
  apiKey: process.env.NEXT_PUBLIC_GEMINI_API_KEY as string,
});

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { prompt, type } = body;

    if (!prompt) {
      return NextResponse.json(
        { error: "Prompt is required" },
        { status: 400 }
      );
    }

    // Create prompt enhancement instructions based on type
    let enhancementInstructions = getEnhancementInstructions(type);

    // Generate enhanced prompt
    const result = await genAI.models.generateContent({
      model: "gemini-2.5-flash",
      contents: `Original  prompt: "${prompt}"`,
      config: {
        maxOutputTokens: 300,
        thinkingConfig: {
          thinkingBudget: 0,
        },
        systemInstruction: `Your task:
          - Rewrite the prompt in enhanced form.
          - DO NOT include any introductory sentence like "Here's the enhanced prompt..."
          - DO NOT include quotes, markdown formatting, or code blocks.
          - Return ONLY the final enhanced prompt text as a single clean string.
          - Use the following instructions based on the type of prompt:
          ${enhancementInstructions}
          `,
      },
    });
    const response = await result.text;
    const enhancedPrompt = response;

    // Return enhanced prompt
    return NextResponse.json({ enhancedPrompt });
  } catch (error) {
    console.error("Error enhancing prompt:", error);
    return NextResponse.json(
      { error: "Failed to enhance prompt" },
      { status: 500 }
    );
  }
}

// Helper function to get enhancement instructions based on type
function getEnhancementInstructions(type: string): string {
  switch (type) {
    case "headshot":
      return `You are a professional portrait photography expert. Enhance the following prompt to create a more detailed description for an AI image generator to create a professional headshot.
      Include specific details about:
      - Lighting setup (key light, fill light, rim light)
      - Camera settings (lens type, aperture)
      - Background details
      - Subject positioning and expression
      - Color grading and mood
      - Professional photography terminology

      The enhanced prompt should be comprehensive but concise, using professional photography terms to create a high-quality portrait image.`;

    case "food":
      return `You are a professional food photography expert. Enhance the following prompt to create a more detailed description for an AI image generator to create mouth-watering food photography.
      Include specific details about:
      - Lighting (natural, studio, direction)
      - Camera angles (overhead, 45-degree, straight-on)
      - Styling elements (props, surfaces, backgrounds)
      - Food details (texture, steam, garnishes)
      - Color palette and mood
      - Professional food photography terminology

      The enhanced prompt should be comprehensive but concise, using professional food photography terms to create an appetizing and visually striking image.`;

    case "product":
      return `You are a professional product photography expert. Enhance the following prompt to create a more detailed description for an AI image generator to create commercial-quality product photography.
      Include specific details about:
      - Studio setup (lighting, reflectors, diffusers)
      - Background and surface
      - Product positioning and angle
      - Highlighting key product features
      - Style (minimalist, lifestyle, in-context)
      - Professional product photography terminology

      The enhanced prompt should be comprehensive but concise, using professional product photography terms to create a high-quality commercial image.`;

    case "virtual try-on":
      return `You are a professional fashion photography expert. Enhance the following prompt to create a more detailed description for an AI image generator to create realistic virtual try-on images of people wearing garments.
      Include specific details about:
      - Fashion photography lighting setup (key light, fill light, rim light)
      - Background and environment
      - Pose and body positioning
      - Garment details and how it should fit/drape on the body
      - Style and mood of the fashion shoot
      - Professional fashion photography terminology

      The enhanced prompt should be comprehensive but concise, using professional fashion photography terms to create a realistic and stylish virtual try-on image. Focus on how the garment interacts with the person wearing it.`;

    default:
      return `You are a professional photography expert. Enhance the following prompt to create a more detailed description for an AI image generator.
      Include specific details about:
      - Lighting
      - Composition
      - Visual style
      - Technical aspects
      - Mood and atmosphere
      - Professional photography terminology

      The enhanced prompt should be comprehensive but concise, using professional terms to create a high-quality image.`;
  }
}
