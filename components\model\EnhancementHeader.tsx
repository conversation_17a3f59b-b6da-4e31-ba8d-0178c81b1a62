"use client";

import React from "react";
import { motion } from "framer-motion";
import { Camera, ShoppingBag, Utensils, Shirt, Home, ArrowUpCircle, UserCircle, Film } from "lucide-react";
import { EnhancementType } from "@/types/componentsProps";

interface EnhancementHeaderProps {
  enhancementType: EnhancementType;
  currentScreen: "modelSelection" | "prompt" | "processing";
}

const EnhancementHeader: React.FC<EnhancementHeaderProps> = ({
  enhancementType,
  currentScreen,
}) => {

  const getEnhancementIcon = () => {
    switch (enhancementType) {
      case "headshot":
        return <Camera className="mr-2 h-5 w-5" />;
      case "product":
        return <ShoppingBag className="mr-2 h-5 w-5" />;
      case "food":
        return <Utensils className="mr-2 h-5 w-5" />;
      case "virtual try-on":
        return <Shirt className="mr-2 h-5 w-5" />;
      case "interior":
        return <Home className="mr-2 h-5 w-5" />;
      case "upscaling":
        return <ArrowUpCircle className="mr-2 h-5 w-5" />;
      case "avatar":
        return <UserCircle className="mr-2 h-5 w-5" />;
      case "image-to-video":
        return <Film className="mr-2 h-5 w-5" />;
      default:
        return null;
    }
  };


  const enhancementTitle =
    enhancementType.charAt(0).toUpperCase() + enhancementType.slice(1);

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className="mb-6 flex  flex-col sm:flex-row space-y-2"
    >
      <div className="flex items-center ">
        {getEnhancementIcon()}
        <h1 className="text-xl font-bold sm:text-2xl">{enhancementTitle} Enhancement</h1>
      </div>

      {/* Progress indicator */}
      <div className="ml-auto flex items-center space-x-1">
        {enhancementType === "avatar" ? (
          <>
            <motion.div
              className={`h-2 w-8 rounded-full ${
                currentScreen === "prompt" ? "bg-primary" : "bg-white/20"
              }`}
              animate={{
                backgroundColor:
                  currentScreen === "prompt" ? "#8B5CF6" : "rgba(255,255,255,0.2)",
              }}
            />
            <motion.div
              className={`h-2 w-8 rounded-full ${
                currentScreen === "processing" ? "bg-primary" : "bg-white/20"
              }`}
              animate={{
                backgroundColor:
                  currentScreen === "processing"
                    ? "#8B5CF6"
                    : "rgba(255,255,255,0.2)",
              }}
            />
          </>
        ) : (
          <>
            <motion.div
              className={`h-2 w-8 rounded-full ${
                currentScreen === "modelSelection" ? "bg-primary" : "bg-white/20"
              }`}
              animate={{
                backgroundColor:
                  currentScreen === "modelSelection"
                    ? "#8B5CF6"
                    : "rgba(255,255,255,0.2)",
              }}
            />
            <motion.div
              className={`h-2 w-8 rounded-full ${
                currentScreen === "prompt" ? "bg-primary" : "bg-white/20"
              }`}
              animate={{
                backgroundColor:
                  currentScreen === "prompt" ? "#8B5CF6" : "rgba(255,255,255,0.2)",
              }}
            />
            <motion.div
              className={`h-2 w-8 rounded-full ${
                currentScreen === "processing" ? "bg-primary" : "bg-white/20"
              }`}
              animate={{
                backgroundColor:
                  currentScreen === "processing"
                    ? "#8B5CF6"
                    : "rgba(255,255,255,0.2)",
              }}
            />
          </>
        )}
      </div>
    </motion.div>
  );
};

export default EnhancementHeader;
