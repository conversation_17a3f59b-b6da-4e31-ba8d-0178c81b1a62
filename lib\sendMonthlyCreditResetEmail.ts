import { Resend } from "resend";
import { monthlyCreditResetEmail } from "@/emailTemplates/monthlyCreditResetEmail";

const resend = new Resend(process.env.RESEND_API_KEY as string);

export async function sendMonthlyCreditResetEmail(
  userName: string,
  planName: string,
  totalCredits: number,
  resetDate: string,
  creditsRemaining: number,
  to: string
) {
  try {
    const emailHtml = await monthlyCreditResetEmail(
      userName,
      planName,
      totalCredits,
      resetDate,
      creditsRemaining
    );

    const { data, error } = await resend.emails.send({
      from: "BurstMode.ai <<EMAIL>>",
      to: [to],
      subject: "Monthly Credit Reset",
      html: emailHtml,
    });

    if (error) {
      console.error("Error sending email:", error.message);
      throw new Error(error.message);
    }

    if (data) {
      console.log("Monthly Credit Reset email sent successfully:", data);
    }
  } catch (error: any) {
    console.error("Error in monthlyCreditResetEmail function:", error);
  }
}
