import React from "react";
import { Download } from "lucide-react";

interface VideoDisplayProps {
  isLoading?: boolean;
  videoUrl: string | null;
  enhancementType?: string;
}

const VideoDisplay: React.FC<VideoDisplayProps> = ({
  isLoading = false,
  videoUrl,
  enhancementType = "video"
}) => {
  // Handle video download
  const handleDownload = async () => {
    if (!videoUrl) return;

    try {
      const encodedUrl = encodeURIComponent(videoUrl);
      const response = await fetch(`/api/proxy/download/${encodedUrl}`);

      if (!response.ok) {
        throw new Error("Failed to download video");
      }

      const blob = await response.blob();
      const downloadLink = document.createElement("a");
      downloadLink.href = URL.createObjectURL(blob);
      downloadLink.download = "generated-video.mp4";

      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
      URL.revokeObjectURL(downloadLink.href);
    } catch (error) {
      console.error("Download failed:", error);
    }
  };

  return (
    <div className="w-full  bg-black/30 p-6 flex flex-col items-center justify-center">
      {isLoading ? (
        <div className="flex flex-col items-center space-y-4">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-2 border-purple-500/20"></div>
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-l-2 border-purple-500 absolute top-0 left-0"></div>
          </div>
          <p className="text-white/70">
            Creating your {enhancementType} video...
          </p>
        </div>
      ) : videoUrl ? (
        <div className="flex flex-col items-center">
          <div className="overflow-hidden rounded-lg shadow-2xl border border-white/10">
            <video
              src={videoUrl}
              controls
              className="w-auto max-h-80 object-contain"
              autoPlay
              loop
              muted
            >
              Your browser does not support the video tag.
            </video>
          </div>

          <button
            onClick={handleDownload}
            className="
              mt-6
              bg-black/60
              hover:bg-black/80
              text-white
              font-medium
              py-2 px-5
              rounded-lg
              transition-all
              duration-200
              shadow-lg
              flex items-center gap-2
              border border-white/10
            "
          >
            <Download size={18} />
            Download
          </button>
        </div>
      ) : (
        <div className="text-center space-y-3 w-full">
          <div className="text-white text-lg opacity-60">
            Your video
          </div>
          <div className="text-white text-sm opacity-40">
            Select a prompt or create your own to generate
          </div>
          <div className="mt-4 text-purple-400/60 text-4xl">
            ✨
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoDisplay;
