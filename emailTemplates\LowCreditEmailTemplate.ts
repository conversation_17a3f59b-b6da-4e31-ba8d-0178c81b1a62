export async function LowCreditEmailTemplate(
  userName: string,
  amount: number,
  totalCredits: number
): Promise<string> {
  const percentageUsed = Math.round((amount / totalCredits) * 100);

  return `
 <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Low Credits Alert</title>
  <!--[if mso]>
    <xml>
      <o:OfficeDocumentSettings>
        <o:AllowPNG/>
        <o:PixelsPerInch>96</o:PixelsPerInch>
      </o:OfficeDocumentSettings>
    </xml>
  <![endif]-->
  <style type="text/css">
    /* General body styling for email clients */
    body, html {
      margin: 0;
      padding: 0;
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
      width: 100% !important;
      font-family: Inter, sans-serif; /* Using Inter, fallback to sans-serif */
      background-color: #f3f4f6; /* Equivalent to bg-gray-100 */
    }
    table, td, div {
      box-sizing: border-box;
    }
    /* Prevent iOS Mail from changing font sizes */
    a[x-apple-data-detectors] {
      color: inherit !important;
      text-decoration: none !important;
      font-size: inherit !important;
      font-family: inherit !important;
      font-weight: inherit !important;
      line-height: inherit !important;
    }
    /* For outlook, to ensure gradient fallback if VML doesn't render */
    .header-background-fallback {
      background-color: #f97316; /* Fallback for gradient */
    }
  </style>
</head>
<body style="margin: 0; padding: 0; min-width: 100%; background-color: #f3f4f6;">
  <div style="min-height: 100vh; background-color: #f3f4f6; display: flex; align-items: center; justify-content: center; padding: 1rem;">
    <div style="max-width: 48rem; width: 100%; margin: auto; background-color: #ffffff; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); border-radius: 0.5rem; overflow: hidden; font-family: Inter, sans-serif;">
      <!-- Header -->
      <table
        width="100%"
        cellpadding="0"
        cellspacing="0"
        border="0"
        role="presentation"
        class="header-background-fallback"
        style="background-color: #f97316; border-collapse: collapse; border-spacing: 0; mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-radius: 0.5rem 0.5rem 0 0;"
      >
        <tr>
          <td align="center" style="padding-left: 2rem; padding-right: 2rem; padding-top: 1.5rem; padding-bottom: 1.5rem;">
            <!--[if (gte mso 9)|(IE)]>
            <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width:100%;height:100%;">
              <v:fill type="gradient" angle="270" from="#f97316" to="#ef4444" />
              <v:textbox style="mso-fit-shape-to-text:true" inset="0,0,0,0">
            <![endif]-->
            <div style="display: flex; align-items: center;">
              <div style="background-color: #ffffff; padding: 0.5rem; border-radius: 0.5rem; margin-right: 1rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);">
                <!-- Zap icon (inline SVG) -->
                <img src="https://www.burstmode.ai/icons/zap.svg" />
              </div>
              <div>
                <h1 style="font-size: 1.5rem; font-weight: 700; color: #ffffff; margin: 0; padding: 0;">BurstMode.ai</h1>
                <p style="color: #fed7aa; font-size: 0.875rem; margin-top: 0.25rem; margin-bottom: 0; padding: 0;">Your AI-Powered Platform</p>
              </div>
            </div>
            <!--[if (gte mso 9)|(IE)]>
              </v:textbox>
            </v:rect>
            <![endif]-->
          </td>
        </tr>
      </table>

      <!-- Alert Section -->
      <div style="padding-left: 2rem; padding-right: 2rem; padding-top: 1.5rem; padding-bottom: 1.5rem; background-color: #fff7ed; border-left: 4px solid #fbbf24;">
        <div style="display: flex; align-items: center;">
          <div style="flex-shrink: 0;">
            <!-- AlertTriangle icon (inline SVG) -->
            <img src="https://www.burstmode.ai/icons/alert.svg" />
          </div>
          <div style="margin-left: 0.75rem;">
            <h2 style="font-size: 1.125rem; font-weight: 600; color: #9a3412; margin: 0; padding: 0;">
              Credit Alert: Only ${percentageUsed}% Remaining
            </h2>
            <p style="color: #c2410c; font-size: 0.875rem; margin-top: 0.25rem; margin-bottom: 0; padding: 0;">
              Your account is running low on credits
            </p>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div style="padding-left: 2rem; padding-right: 2rem; padding-top: 2rem; padding-bottom: 2rem;">
        <h3 style="font-size: 1.25rem; font-weight: 600; color: #1f2937; margin-bottom: 1rem; padding: 0;">
          Hi ${userName},
        </h3>

        <p style="color: #374151; margin-bottom: 1.5rem; line-height: 1.625; padding: 0;">
          We wanted to let you know that your BurstMode.ai account is running low on credits.
          You currently have <span style="font-weight: 600; color: #ea580c;">${amount} credits</span> remaining
          out of your total <span style="font-weight: 600;">${totalCredits} credits</span>.
        </p>

        <!-- Credit Progress Bar -->
        <div style="margin-bottom: 2rem;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
            <span style="font-size: 0.875rem; font-weight: 500; color: #374151; margin-right: 8px;">Credit Usage</span>
            <span style="font-size: 0.875rem; color: #6b7280;">${amount} / ${totalCredits} credits</span>
          </div>
          <div style="width: 100%; background-color: #e5e7eb; border-radius: 9999px; height: 0.75rem;">
            <div
              style="background-image: linear-gradient(to right, #fbbf24, #ef4444); height: 0.75rem; border-radius: 9999px; transition-property: all; transition-duration: 300ms; width: ${100 - percentageUsed}%;"
            ></div>
          </div>
          <p style="font-size: 0.75rem; color: #6b7280; margin-top: 0.25rem; margin-bottom: 0; padding: 0;">${percentageUsed || 20}% remaining</p>
        </div>

        <div style="background-color: #eff6ff; border: 1px solid #bfdbfe; border-radius: 0.5rem; padding: 1.5rem; margin-bottom: 2rem;">
          <h4 style="font-size: 1.125rem; font-weight: 600; color: #1e3a8a; margin-bottom: 0.75rem; padding: 0;">
            Don't let your creativity stop here!
          </h4>
          <a href="https://www.burstmode.ai/pricing" target="_blank" rel="noopener noreferrer" style="color: #1f3780; margin-bottom: 1rem; padding: 0; display: block;">
            Upgrade your plan to continue enjoying uninterrupted access to our AI-powered features:
          </a>
          
          <ul style="color: #1c458a; list-style: none; padding: 0; margin: 0; margin-bottom: 1rem;">
            <li style="display: flex; align-items: center; margin-bottom: 0.5rem;">
              <div style="width: 0.5rem; height: 0.5rem; background-color: #60a5fa; border-radius: 9999px; margin-right: 0.75rem;"></div>
              More credits for extended usage
            </li>
            <li style="display: flex; align-items: center; margin-bottom: 0.5rem;">
              <div style="width: 0.5rem; height: 0.5rem; background-color: #60a5fa; border-radius: 9999px; margin-right: 0.75rem;"></div>
              Priority processing and faster results
            </li>
            <li style="display: flex; align-items: center;">
              <div style="width: 0.5rem; height: 0.5rem; background-color: #60a5fa; border-radius: 9999px; margin-right: 0.75rem;"></div>
              Access to premium features
            </li>
          </ul>
        </div>

        <!-- CTA Button -->
        <div style="text-align: center; margin-bottom: 2rem;">
          <a
            href="https://www.burstmode.ai/pricing"
            target="_blank"
            rel="noopener noreferrer"
            style="display: inline-flex; align-items: center; padding: 1rem 2rem; background-image: linear-gradient(to right, #2563eb, #9333ea); color: #ffffff; font-weight: 600; border-radius: 0.5rem; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); text-decoration: none; transition-property: all; transition-duration: 200ms;"
          >
            <!-- CreditCard icon (inline SVG) -->
            <img src="https://www.burstmode.ai/icons/credit-card.svg" />
            Upgrade Your Plan
            <!-- ArrowRight icon (inline SVG) -->
           <img src="https://www.burstmode.ai/icons/arrow-right.svg" />
          </a>
        </div>

        <!-- Alternative Options -->
        <div style="background-color: #f9fafb; border-radius: 0.5rem; padding: 1.5rem;">
          <h4 style="font-weight: 600; color: #1f2937; margin-bottom: 0.75rem; padding: 0;">Need help choosing?</h4>
          <p style="color: #4b5563; font-size: 0.875rem; margin-bottom: 1rem; padding: 0;">
            Our team is here to help you find the perfect plan for your needs.
          </p>
          <!-- Each link in its own row for full width -->
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="border-collapse: collapse; border-spacing: 0;">
            <tr>
              <td style="padding-bottom: 0.5rem;"> <!-- Add vertical padding here for spacing between links -->
                <a
                  href="https://www.burstmode.ai/contact-us"
                  style="color: #2563eb; text-decoration: none; font-size: 0.875rem; font-weight: 500; display: block; padding: 0.25rem 0;"
                >
                  Contact Support
                </a>
              </td>
            </tr>
            <tr>
              <td style="padding-bottom: 0.5rem;">
                <a
                  href="https://www.burstmode.ai/pricing"
                  style="color: #2563eb; text-decoration: none; font-size: 0.875rem; font-weight: 500; display: block; padding: 0.25rem 0;"
                >
                  View Pricing Plans
                </a>
              </td>
            </tr>
            <tr>
              <td>
                <a
                  href="https://www.burstmode.ai/faq"
                  style="color: #2563eb; text-decoration: none; font-size: 0.875rem; font-weight: 500; display: block; padding: 0.25rem 0;"
                >
                  Billing FAQ
                </a>
              </td>
            </tr>
          </table>
        </div>
      </div>

      <!-- Footer -->
      <div style="background-color: #f3f4f6; padding-left: 2rem; padding-right: 2rem; padding-top: 1.5rem; padding-bottom: 1.5rem; border-top: 1px solid #e5e7eb; border-radius: 0 0 0.5rem 0.5rem;">
        <p style="color: #4b5563; font-size: 0.875rem; margin-bottom: 1rem; padding: 0;">
          Thank you for choosing BurstMode.ai. We're committed to powering your creative projects with cutting-edge AI technology.
        </p>
        <!-- Using a table for footer links for better compatibility -->
        <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0" style="border-collapse: collapse; border-spacing: 0;">
          <tr>
            <td style="font-size: 0.75rem; color: #6b7280; vertical-align: top; padding-right: 1rem; width: 50%;">
              © 2025 BurstMode.ai. All rights reserved.
            </td>
            <td style="font-size: 0.75rem; vertical-align: top; text-align: right;">
              <a href="#unsubscribe" style="color: #6b7280; text-decoration: none; margin-left: 10px;">Unsubscribe</a>
              <a href="https://www.burstmode.ai/privacy-policy" style="color: #6b7280; text-decoration: none; margin-left: 10px;">Privacy Policy</a>
              <a href="https://www.burstmode.ai/terms" style="color: #6b7280; text-decoration: none; margin-left: 10px;">Terms of Service</a>
            </td>
          </tr>
        </table>
      </div>
    </div>
  </div>
</body>
</html>
`;
}
