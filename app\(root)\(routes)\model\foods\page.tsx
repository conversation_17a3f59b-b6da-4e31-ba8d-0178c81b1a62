import React from "react";
import EnhancementWrapper from "@/components/model/EnhancementWrapper";
import { Metadata } from 'next';
import Script from 'next/script';

export const metadata: Metadata = {
  title: "AI Food Photography Enhancement | Burst Mode",
  description: "Transform your food photos with Burst Mode's AI enhancement. Create stunning, professional-quality food photography for menus, social media, and marketing materials.",
  keywords: [
    "AI food photography",
    "food photo enhancement",
    "restaurant menu photos",
    "food marketing images",
    "culinary photography AI",
    "food styling AI",
    "professional food photos",
    "food blogger photography",
    "restaurant photography",
    "food presentation enhancement"
  ],
  openGraph: {
    title: "AI Food Photography Enhancement | Burst Mode",
    description: "Transform your food photos with Burst Mode's AI enhancement. Create stunning, professional-quality food photography for menus, social media, and marketing materials.",
    url: "https://burstmode.ai/model/foods",
    siteName: "Burst Mode AI",
    images: [
      {
        url: "https://burstmode.ai/og-enhanced_food.jpg",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Food Photography Enhancement",
      }
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "AI Food Photography Enhancement | Burst Mode",
    description: "Transform your food photos with Burst Mode's AI enhancement for menus, social media, and marketing.",
    images: [
      {
        url: "https://burstmode.ai/app/og-enhanced_food.jpg",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Food Photography Enhancement",
      }
    ],
  },
  alternates: {
    canonical: "https://burstmode.ai/model/foods",
  },
};

const FoodEnhancementPage = () => {
  return (
    <>
      {/* Structured Data for SEO */}
      <Script
        id="food-enhancement-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "AI Food Photography Enhancement",
            "description": "Transform your food photos with Burst Mode's AI enhancement. Create stunning, professional-quality food photography for menus, social media, and marketing materials.",
            "url": "https://burstmode.ai/model/foods",
            "mainEntity": {
              "@type": "Service",
              "name": "AI Food Photography Enhancement",
              "provider": {
                "@type": "Organization",
                "name": "Burst Mode AI",
                "url": "https://burstmode.ai"
              },
              "description": "Our AI-powered food photography enhancement service transforms ordinary food photos into stunning, professional-quality images. Perfect for restaurants, food bloggers, and marketing materials.",
              "offers": {
                "@type": "Offer",
                "price": "15.00",
                "priceCurrency": "USD"
              },
              "serviceType": "AI Photography Enhancement"
            }
          })
        }}
      />
      <EnhancementWrapper enhancementType="food" />
    </>
  );
};

export default FoodEnhancementPage;